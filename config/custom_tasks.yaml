# Custom Task Definitions
# Define your automation workflows here

workflows:
  morning_routine:
    name: "Morning Routine"
    description: "Complete morning automation workflow"
    tasks:
      - task: browser_login
        params:
          site: gmail
      - task: email_automation
        params:
          action: check
      - task: social_media_post
        params:
          platform: twitter
          content: "Good morning! Starting my day with automation 🤖"
      - task: system_info

  content_creation:
    name: "Content Creation Workflow"
    description: "Research and create content"
    tasks:
      - task: web_research
        params:
          query: "latest AI developments"
      - task: data_extraction
        params:
          url: "https://news.ycombinator.com"
      - task: social_media_post
        params:
          platform: linkedin
          content: "Sharing insights from today's research"

  weekly_maintenance:
    name: "Weekly System Maintenance"
    description: "Weekly cleanup and backup routine"
    tasks:
      - task: backup_browser
      - task: organize_files
        params:
          path: "~/Downloads"
      - task: system_info
      - task: test_automation

# Browser automation settings
browser_settings:
  default_browser: "chrome"
  headless: false
  window_size: "1920x1080"
  user_data_dir: "~/chrome_profile"
  extensions:
    - "ublock_origin"
    - "lastpass"
    - "grammarly"

# Site-specific configurations
sites:
  gmail:
    url: "https://gmail.com"
    login_selector: "#identifierId"
    password_selector: "#password input"
    two_factor: true
    
  twitter:
    url: "https://twitter.com"
    compose_selector: "[data-testid='tweetTextarea_0']"
    post_button: "[data-testid='tweetButton']"
    
  linkedin:
    url: "https://linkedin.com"
    post_selector: ".ql-editor"
    share_button: "[data-control-name='share.post']"

# Automation preferences
preferences:
  screenshot_on_error: true
  retry_attempts: 3
  delay_between_actions: 1.0
  save_session_data: true
  backup_frequency: "daily"
