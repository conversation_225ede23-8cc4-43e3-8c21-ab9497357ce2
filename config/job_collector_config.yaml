# Job Collector Configuration
# This file contains the configuration for the job listing collection system

# Core settings
output_directory: "./output/job_collector"
log_level: "INFO"
user_agent: "JobCollector/1.0"
default_request_delay: 1.0
respect_robots_txt: true
max_retries: 3

# Platform configurations
platforms:
  linkedin:
    enabled: true
    credentials:
      # Add your LinkedIn credentials here
      username: ""
      password: ""
    rate_limits:
      requests_per_minute: 30
      requests_per_hour: 500
    custom_settings:
      use_api: false
      headless_browser: true

  indeed:
    enabled: true
    credentials: {}
    rate_limits:
      requests_per_minute: 60
      requests_per_hour: 1000
    custom_settings:
      use_api: false
      headless_browser: true

  glassdoor:
    enabled: false
    credentials:
      api_key: ""
    rate_limits:
      requests_per_minute: 20
      requests_per_hour: 200
    custom_settings:
      use_api: true

# Storage configuration
storage:
  type: "json"  # Options: json, sqlite, postgresql
  file_path: "./output/job_collector/jobs.json"
  backup_enabled: true
  retention_days: 90

# Notification settings
notifications:
  enabled: false
  email_enabled: false
  email_settings:
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from_email: ""
    to_emails: []
  webhook_enabled: false
  webhook_url: ""
  slack_enabled: false
  slack_webhook: ""

# Search configuration
search:
  default_max_results: 100
  default_posted_since_days: 7
  enable_duplicate_detection: true
  auto_save_results: true
  parallel_searches: false
  max_concurrent_searches: 3
