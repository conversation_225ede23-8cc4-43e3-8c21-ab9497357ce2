# CUA Agent Configuration

# Agent Settings
agent:
  name: "PersonalCUAAgent"
  description: "A versatile computer-use agent for automation tasks"
  version: "1.0.0"
  
# VM Configuration
vm:
  display: "1024x768"
  memory: "8GB"
  cpu: "4"
  os_type: "macos"
  ephemeral: false
  provider_type: "lume"

# AI Provider Configuration
ai:
  default_provider: "anthropic"
  default_model: "claude-3-5-sonnet-20241022"
  max_tokens: 4096
  temperature: 0.1
  
  providers:
    anthropic:
      models:
        - "claude-3-5-sonnet-20241022"
        - "claude-3-5-haiku-20241022"
    openai:
      models:
        - "gpt-4o"
        - "gpt-4o-mini"
    openrouter:
      models:
        - "anthropic/claude-3.5-sonnet"
        - "openai/gpt-4o"

# Task Configuration
tasks:
  web_automation:
    enabled: true
    browser: "chrome"
    headless: false
    timeout: 30
    
  file_management:
    enabled: true
    watch_directories:
      - "~/Downloads"
      - "~/Desktop"
    backup_enabled: true
    
  development:
    enabled: true
    default_editor: "cursor"
    git_auto_commit: false
    
  data_processing:
    enabled: true
    output_formats: ["json", "csv", "xlsx"]

# Safety Settings
safety:
  require_confirmation: true
  auto_screenshot: true
  max_actions_per_task: 50
  dangerous_commands:
    - "rm -rf"
    - "sudo"
    - "format"
    - "delete"

# Logging Configuration
logging:
  level: "INFO"
  file_logging: true
  console_logging: true
  log_rotation: true
  max_log_files: 10

# Output Configuration
output:
  base_directory: "./output"
  screenshots_enabled: true
  save_intermediate_results: true
  compress_old_files: true

# Monitoring
monitoring:
  system_metrics: true
  performance_tracking: true
  error_reporting: true
