# AI Provider API Keys
ANTHROPIC_API_KEY=your_anthropic_key_here
OPENAI_API_KEY=your_openai_key_here
OPENROUTER_API_KEY=your_openrouter_key_here

# CUA Configuration
CUA_TELEMETRY=off
PYLUME_HOST=localhost
PYLUME_PORT=8080

# Project Configuration
PROJECT_NAME=cua-agent
OUTPUT_DIR=./output
LOG_LEVEL=INFO
DRY_RUN=false

# VM Configuration
VM_DISPLAY=1024x768
VM_MEMORY=8GB
VM_CPU=4
VM_OS_TYPE=macos
VM_EPHEMERAL=false

# Default AI Provider Settings
DEFAULT_PROVIDER=openai
DEFAULT_MODEL=gpt-4o
MAX_TOKENS=4096
TEMPERATURE=0.1

# Safety Settings
REQUIRE_CONFIRMATION=true
AUTO_SCREENSHOT=true
MAX_ACTIONS_PER_TASK=50

# File Management
DOWNLOADS_PATH=~/Downloads
WORKSPACE_PATH=~/workspace
BACKUP_PATH=~/backups

# Web Automation
DEFAULT_BROWSER=chrome
BROWSER_HEADLESS=false
PAGE_TIMEOUT=30

# Development Settings
DEFAULT_EDITOR=cursor
GIT_USERNAME=your_git_username
GIT_EMAIL=your_git_email

# CUA Framework Path
CUA_FRAMEWORK_PATH=~/util/cua
