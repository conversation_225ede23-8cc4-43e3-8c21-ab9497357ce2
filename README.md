# CUA Agent Project

A practical Computer Use Agent (CUA) built for automating various tasks on macOS, with support for both local execution and virtualized environments using LUME.

## What is CUA Agent?

The CUA (Computer Use Agent) is an intelligent automation system that can interact with your computer to perform tasks like web browsing, file management, development assistance, and system administration. This project provides a simplified, working implementation of the CUA framework that can run in multiple deployment scenarios.

### Key Capabilities

- **Web Automation**: Browse websites, fill forms, extract data
- **File Management**: Organize files, process documents, manage downloads
- **Development Tasks**: Code assistance, project setup, git operations
- **System Administration**: Monitor system, manage applications
- **Data Processing**: Extract, transform, and analyze data
- **Interactive Mode**: Real-time command execution with rich terminal interface

## Architecture Overview

This project integrates several components:

- **CUA Framework**: The core computer-use automation framework from [trycua/cua](https://github.com/trycua/cua)
- **LUME**: macOS virtualization system for running agents in isolated VM environments
- **Simple Agent**: A lightweight, working implementation that demonstrates CUA capabilities
- **Rich CLI**: Interactive terminal interface with beautiful formatting and real-time feedback

## Prerequisites

Before setting up the CUA Agent, ensure you have:

- **Python 3.11+** (required for CUA framework compatibility)
- **macOS** (for LUME VM support)
- **Anthropic API Key** (recommended) or OpenAI API Key
- **LUME** (optional, for VM-based deployment)
- **Git** (for cloning dependencies)

## Installation

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/nebitessema/cua_agent.git
cd cua_agent

# Create and activate virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. API Key Configuration

Create a `.env` file with your API keys:

```bash
# Create environment file
touch .env

# Add your API keys (choose one or both)
echo "ANTHROPIC_API_KEY=your_anthropic_key_here" >> .env
echo "OPENAI_API_KEY=your_openai_key_here" >> .env
```

### 3. Verify Installation

```bash
# Test the simple agent
python examples/simple_agent.py --interactive

# Or run a quick CUA agent test
python run_tests.py

# Or run a specific test
cd tests/agent
python simple_recorded_test.py
```

## Deployment Options

### Option A: Local Execution (Recommended for Development)

Run the agent directly on your local machine:

```bash
# Interactive mode
python simple_agent.py --interactive

# Run specific tasks
python simple_agent.py --task web_research --query "latest AI developments"
python simple_agent.py --task organize_files --path ~/Downloads
python simple_agent.py --task system_info
```

### Option B: VM-Based Execution with LUME

For isolated execution and enhanced security, run the agent in a macOS virtual machine:

#### 1. Install LUME (if not already installed)

```bash
# Install LUME
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/trycua/cua/main/libs/lume/scripts/install.sh)"
```

#### 2. Set up macOS VM

```bash
# Pull the CUA-compatible macOS image
lume pull macos-sequoia-cua:latest

# Create and start VM with shared directory
lume run macos-sequoia-cua_latest --shared-dir ~/workspace/spring25/cua_agent:rw
```

#### 3. Access VM and run agent

```bash
# Connect via SSH (password: lume)
ssh lume@************

# Or connect via VNC for visual access
# VNC URL will be displayed when VM starts
```

#### 4. Set up agent in VM

```bash
# In the VM terminal
cd "/Volumes/My Shared Files"
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Run the agent
python simple_agent.py --interactive
```

## Project Structure

```
cua_agent/
├── run_tests.py           # Main test runner
├── requirements.txt       # Python dependencies
├── .env                  # Your API keys (not in git)
├── config/               # Configuration files
│   ├── settings.yaml     # Agent configuration
│   └── job_collector_config.yaml
├── src/                  # Source code
│   └── job_collector/    # Job collection system
├── examples/             # Example implementations
│   ├── simple_agent.py   # Main working agent implementation
│   └── cua_demo.py      # CUA framework demo
├── tests/                # Organized test suite
│   ├── agent/           # Agent functionality tests
│   ├── integration/     # Integration tests
│   ├── debug/          # Debug utilities
│   └── manual/         # Manual testing tools
├── infra/               # Infrastructure setup
│   └── lume/           # LUME VM configuration
├── output/             # Screenshots, logs, results
├── session_recordings/ # Test session recordings
├── docs/              # Documentation
│   └── adr/           # Architecture Decision Records
└── README.md          # This file
```

## Usage Examples

### Running Tests

```bash
# Run the test suite (recommended)
python run_tests.py

# Or run specific test categories
cd tests/agent
python simple_recorded_test.py    # Quick agent test with recording
python test_with_recording.py     # Full agent test suite

cd tests/integration
python test_video_recording.py    # Test video recording

cd tests/manual
python view_sessions.py           # View test session recordings
```

### Interactive Mode (Examples)
```bash
# Start interactive session
python examples/simple_agent.py --interactive

# Available commands:
# help     - Show available commands
# tasks    - List available tasks
# run <task> - Execute a specific task
# quit     - Exit the agent
```

### Command Line Tasks (Examples)
```bash
# Web research
python examples/simple_agent.py --task web_research --query "CUA framework documentation"

# File organization analysis
python examples/simple_agent.py --task organize_files --path ~/Downloads

# System information
python examples/simple_agent.py --task system_info

# Test automation capabilities
python examples/simple_agent.py --task test_automation
```

## Available Tasks

The agent currently supports these tasks:

| Task | Description | Usage |
|------|-------------|-------|
| `web_research` | Simulate web research and data collection | `run web_research` |
| `organize_files` | Analyze file organization in directories | `run organize_files` |
| `system_info` | Display system resource information | `run system_info` |
| `test_automation` | Test basic automation capabilities | `run test_automation` |
| `job_search` | Collect job listings from various platforms | `run job_search` |

## Configuration

The agent can be configured through:

1. **Environment variables** (`.env` file) - API keys and basic settings
2. **YAML configuration** (`config/settings.yaml`) - Advanced agent behavior
3. **Command line arguments** - Runtime parameters

### Environment Variables

```bash
# Required: At least one AI provider API key
ANTHROPIC_API_KEY=your_anthropic_key_here
OPENAI_API_KEY=your_openai_key_here

# Optional: Logging and output settings
LOG_LEVEL=INFO
OUTPUT_DIR=./output
```

## Supported AI Providers

- **Anthropic Claude** (recommended) - Best performance for computer use tasks
- **OpenAI GPT** - Alternative provider with good capabilities
- **OpenRouter** - Access to multiple models through single API

## Safety Features

- **Confirmation prompts** for potentially destructive operations
- **Dry-run mode** for testing without executing actions
- **Detailed logging** of all actions and decisions
- **Result capture** with timestamps and metadata
- **Isolated execution** when using VM deployment

## Troubleshooting

### Common Issues

**1. "No API keys found" error**
```bash
# Verify your .env file exists and contains valid keys
cat .env
# Should show: ANTHROPIC_API_KEY=sk-...
```

**2. Import errors or missing dependencies**
```bash
# Reinstall dependencies
pip install -r requirements.txt

# Or install specific missing packages
pip install anthropic rich python-dotenv
```

**3. VM connection issues (LUME deployment)**
```bash
# Check VM status
lume ls

# Restart VM if needed
lume stop macos-sequoia-cua_latest
lume run macos-sequoia-cua_latest --shared-dir ~/workspace/spring25/cua_agent:rw
```

**4. Permission errors in VM**
```bash
# Ensure shared directory has proper permissions
chmod -R 755 ~/workspace/spring25/cua_agent
```

### Getting Help

1. **Check logs**: Results and logs are saved in `./output/`
2. **Run tests**: Use `python simple_agent.py --task test_automation`
3. **Interactive mode**: Use `python simple_agent.py --interactive` for step-by-step execution
4. **GitHub Issues**: Report bugs at [github.com/nebitessema/cua_agent/issues](https://github.com/nebitessema/cua_agent/issues)

## CUA Framework Integration

This project integrates with the broader CUA ecosystem:

- **CUA Core**: Installed via `pip install git+https://github.com/trycua/cua.git`
- **LUME**: macOS virtualization system for isolated agent execution
- **Computer Interface**: Screen interaction and automation capabilities
- **Agent Framework**: Task orchestration and AI provider integration

For advanced usage with the full CUA framework, see `main.py` which demonstrates deeper integration.

## Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository** on GitHub
2. **Create a feature branch**: `git checkout -b feature/new-task`
3. **Add your changes**:
   - New tasks go in `simple_agent.py`
   - Update task list in `show_available_tasks()`
   - Add usage examples to README
4. **Test thoroughly**: Run all existing tasks to ensure compatibility
5. **Submit a pull request** with clear description of changes

### Adding New Tasks

```python
def your_new_task(self, **kwargs):
    """Your new task description."""
    console.print("🎯 Running your new task...")

    # Task implementation here
    result = {"status": "completed"}

    self.save_results("your_new_task", result)
    console.print("[green]✅ Task completed![/green]")
    return True
```

## License

MIT License - see LICENSE file for details.

## Acknowledgments

- [CUA Framework](https://github.com/trycua/cua) - Core computer-use automation
- [LUME](https://github.com/trycua/lume) - macOS virtualization system
- [Anthropic](https://anthropic.com) - Claude AI models
- [Rich](https://github.com/Textualize/rich) - Beautiful terminal formatting
