# CUA Job Collector - Phase 1 & 2 Action Plan

## Executive Summary

Fix the "fake success" problem where VMs never actually start, then build a working job collector. Focus on practical, verifiable steps that get the CUA framework actually working with visible VMs.

**Core Problem**: Tests claim success but use incorrect `Computer()` initialization, leading to agents clicking on black screens.

---

## **Phase 1: Establish Reliable CUA Session Management**

**Objective**: Fix VM startup and create reusable `CUASessionManager` with proper lifecycle management.

### **Task 1.1: Create Baseline VM Verification**

**File**: `scripts/verify_vm_lifecycle.py`

```python
#!/usr/bin/env python3
"""Verify that CUA VM lifecycle works correctly."""

import asyncio
from computer import Computer
from agent import ComputerAgent, LLM, LLMProvider, AgentLoop

async def main():
    print("🚀 Testing CUA VM Lifecycle...")
    
    try:
        # Use correct async context manager pattern
        async with Computer(os_type="macos") as computer:
            print("✅ VM Started Successfully")
            print(f"   VM Status: Active")
            print(f"   VM IP: {getattr(computer, 'ip_address', 'N/A')}")
            
            # Test basic interaction
            screenshot = await computer.interface.screenshot()
            print(f"✅ Screenshot captured: {len(screenshot)} bytes")
            
            # Test agent creation
            agent = ComputerAgent(
                computer=computer,
                loop=AgentLoop.OMNI,
                model=LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
            )
            print("✅ Agent created successfully")
            
            # Simple agent task
            print("🤖 Testing agent interaction...")
            async for response in agent.run("Take a screenshot and describe what you see"):
                print(f"   Agent: {response}")
                break
                
        print("✅ VM shutdown complete")
        
    except Exception as e:
        print(f"❌ VM lifecycle test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
```

**Success Criteria**:
- Script runs without errors
- VM window becomes visible during execution
- Agent provides meaningful response about desktop
- VM shuts down cleanly

### **Task 1.2: Implement CUASessionManager**

**File**: `src/cua_session_manager.py`

```python
"""CUA Session Manager - Proper VM lifecycle management."""

import asyncio
from typing import Optional
from computer import Computer
from agent import ComputerAgent, LLM, LLMProvider, AgentLoop

class CUASessionManager:
    """Manages CUA Computer lifecycle with proper async context handling."""
    
    def __init__(self, os_type: str = "macos", provider: str = "openai", model: str = "gpt-4o"):
        self.os_type = os_type
        self.provider = provider
        self.model = model
        self._computer: Optional[Computer] = None
        self._agent: Optional[ComputerAgent] = None
    
    async def __aenter__(self):
        print("🚀 CUASessionManager: Starting VM...")
        self._computer = Computer(os_type=self.os_type)
        # This is the key fix - actually start the VM!
        await self._computer.__aenter__()
        print("✅ CUASessionManager: VM active")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        print("🔄 CUASessionManager: Shutting down...")
        if self._computer:
            await self._computer.__aexit__(exc_type, exc_val, exc_tb)
            self._computer = None
        self._agent = None
        print("✅ CUASessionManager: Shutdown complete")
    
    def get_computer(self) -> Computer:
        """Get the active Computer instance."""
        if not self._computer:
            raise RuntimeError("Session not active. Use 'async with CUASessionManager()'")
        return self._computer
    
    def get_agent(self) -> ComputerAgent:
        """Get or create a ComputerAgent instance."""
        if not self._computer:
            raise RuntimeError("Session not active")
        
        if not self._agent:
            # Create agent based on provider
            if self.provider == "openai":
                llm = LLM(provider=LLMProvider.OPENAI, name=self.model)
            elif self.provider == "anthropic":
                llm = LLM(provider=LLMProvider.ANTHROPIC, name=self.model)
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")
            
            self._agent = ComputerAgent(
                computer=self._computer,
                loop=AgentLoop.OMNI,  # Use OMNI loop for compatibility
                model=llm
            )
        
        return self._agent
```

### **Task 1.3: Create Session Manager Test**

**File**: `tests/test_session_manager.py`

```python
"""Test CUASessionManager functionality."""

import pytest
import asyncio
from src.cua_session_manager import CUASessionManager

@pytest.mark.asyncio
async def test_session_manager_lifecycle():
    """Test that session manager properly starts and stops VM."""
    
    async with CUASessionManager() as session:
        # Test computer access
        computer = session.get_computer()
        assert computer is not None
        
        # Test screenshot (proves VM is running)
        screenshot = await computer.interface.screenshot()
        assert len(screenshot) > 1000  # Should be substantial
        
        # Test agent creation
        agent = session.get_agent()
        assert agent is not None

def test_session_manager_error_outside_context():
    """Test that accessing session outside context raises error."""
    session = CUASessionManager()
    
    with pytest.raises(RuntimeError):
        session.get_computer()
    
    with pytest.raises(RuntimeError):
        session.get_agent()
```

### **Task 1.4: Fix Existing CUA Indeed Adapter**

**Action**: Update `src/job_collector/adapters/cua_indeed_adapter.py` to use `CUASessionManager`

**Key Changes**:
- Replace manual `Computer()` creation with `CUASessionManager`
- Use proper async context management
- Remove the broken `_setup_agent` method

---

## **Phase 2: Build Simple Job Collector**

**Objective**: Prove end-to-end value with a working Indeed job collector.

### **Task 2.1: Define Job Data Schema**

**File**: `src/schemas.py`

```python
"""Data schemas for job collection."""

from pydantic import BaseModel, HttpUrl
from typing import Optional
from datetime import datetime

class JobPosting(BaseModel):
    """Structured job posting data."""
    title: str
    company: str
    location: str
    url: HttpUrl
    description: Optional[str] = None
    salary_min: Optional[int] = None
    salary_max: Optional[int] = None
    posted_date: Optional[datetime] = None
    source: str = "indeed"
```

### **Task 2.2: Implement Simple Indeed Collector**

**File**: `src/collectors/simple_indeed_collector.py`

```python
"""Simple Indeed job collector using CUA."""

from typing import List
from src.cua_session_manager import CUASessionManager
from src.schemas import JobPosting

class SimpleIndeedCollector:
    """Single-purpose Indeed collector to prove CUA concept."""
    
    def __init__(self, session_manager: CUASessionManager):
        self.session_manager = session_manager
    
    async def collect_jobs(self, query: str, location: str, max_jobs: int = 5) -> List[JobPosting]:
        """Collect jobs from Indeed using CUA agent."""
        
        agent = self.session_manager.get_agent()
        
        # Simple, focused task for the agent
        task = f"""
        Navigate to Indeed.com and search for jobs:
        1. Go to https://www.indeed.com
        2. Search for '{query}' jobs in '{location}'
        3. Wait for results to load
        4. Extract the first {max_jobs} job listings
        
        For each job, get: title, company, location, and URL.
        """
        
        print(f"🤖 Agent task: Search for '{query}' in '{location}'")
        
        # Execute the task
        async for response in agent.run(task):
            print(f"   Agent: {response}")
            # For now, just log the response
            # In a real implementation, we'd parse the response
            # and extract structured data
            break
        
        # TODO: Parse agent response and extract job data
        # For Phase 2, return mock data to prove the flow works
        return [
            JobPosting(
                title=f"Sample {query} Job",
                company="Sample Company",
                location=location,
                url="https://indeed.com/sample-job"
            )
        ]
```

### **Task 2.3: Create Main Execution Script**

**File**: `scripts/run_simple_collector.py`

```python
"""Run the simple Indeed collector."""

import asyncio
from src.cua_session_manager import CUASessionManager
from src.collectors.simple_indeed_collector import SimpleIndeedCollector

async def main():
    """Run a simple job collection test."""
    
    print("🚀 Simple Indeed Collector Test")
    print("=" * 50)
    
    async with CUASessionManager() as session:
        collector = SimpleIndeedCollector(session)
        
        jobs = await collector.collect_jobs(
            query="Python Developer",
            location="San Francisco, CA",
            max_jobs=3
        )
        
        print(f"\n📊 Results: {len(jobs)} jobs collected")
        for i, job in enumerate(jobs, 1):
            print(f"\n{i}. {job.title}")
            print(f"   Company: {job.company}")
            print(f"   Location: {job.location}")
            print(f"   URL: {job.url}")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## **Success Criteria**

### **Phase 1 Success**:
- [ ] `verify_vm_lifecycle.py` shows visible VM window
- [ ] Agent provides meaningful desktop description
- [ ] `CUASessionManager` tests pass
- [ ] No more "fake success" - failures are real failures

### **Phase 2 Success**:
- [ ] `run_simple_collector.py` executes without errors
- [ ] Agent successfully navigates to Indeed.com
- [ ] Structured job data is returned (even if mocked)
- [ ] End-to-end flow is verifiable and observable

---

## **Testing Strategy**

1. **Manual Verification**: Run scripts and visually confirm VM behavior
2. **Unit Tests**: Test individual components in isolation
3. **Integration Tests**: Test CUA framework integration
4. **End-to-End Tests**: Full workflow validation

**Key Principle**: Every test must fail if the underlying functionality doesn't work. No more "fake success."
