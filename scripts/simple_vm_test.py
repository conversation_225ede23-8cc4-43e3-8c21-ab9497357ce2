#!/usr/bin/env python3
"""Simple VM test - actually start a VM and see if it opens.

This is a minimal test to see if we can actually start a VM
and have it be visible, not just connect to existing ones.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.cua_session_manager import CUASessionManager


async def simple_vm_test():
    """Simple test - start a VM and see if it opens."""
    print("🚀 SIMPLE VM TEST")
    print("=" * 40)
    print("This should open a VM window that you can see.")
    print("If no window opens, then our code is broken.")
    print()
    
    try:
        print("💻 Starting VM with CUASessionManager...")
        async with CUASessionManager() as session:
            print("✅ CUASessionManager says it started")
            
            # Take a screenshot to prove it's working
            screenshot = await session.take_screenshot()
            print(f"📸 Screenshot captured: {len(screenshot)} bytes")
            
            # Keep it running for 30 seconds so you can see it
            print("\n⏱️  Keeping VM running for 30 seconds...")
            print("   You should see a VM window open now.")
            print("   If you don't see anything, the test is lying.")
            
            for i in range(6):
                await asyncio.sleep(5)
                print(f"   Still running... {(i+1)*5}s")
            
            print("\n✅ Test complete - VM should have been visible")
            return True
            
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False


async def main():
    """Run the simple VM test."""
    success = await simple_vm_test()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Test completed successfully!")
        print("   Did you see a VM window open? If not, something is wrong.")
    else:
        print("❌ Test failed with errors.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
