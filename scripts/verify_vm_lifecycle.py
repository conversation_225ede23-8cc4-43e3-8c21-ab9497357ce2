#!/usr/bin/env python3
"""
Verify that CUA VM lifecycle works correctly.

This script tests the fundamental VM startup issue we identified:
- Tests claim success but VMs never actually start
- Missing proper 'async with Computer()' pattern
- Agent sees black screenshots because VM isn't running

This script will prove whether the VM actually starts and is visible.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from dotenv import load_dotenv
load_dotenv()

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError as e:
    print(f"❌ CUA framework not available: {e}")
    CUA_AVAILABLE = False


async def test_vm_lifecycle():
    """Test the fundamental VM lifecycle issue."""
    print("🚀 Testing CUA VM Lifecycle...")
    print("=" * 50)
    print("This test will:")
    print("1. Start a VM using the correct async context pattern")
    print("2. Verify the VM is actually running (you should see a window)")
    print("3. Take a screenshot to prove it's working")
    print("4. Create an agent and test basic interaction")
    print("5. Shut down cleanly")
    print()
    
    if not CUA_AVAILABLE:
        print("❌ Cannot run test - CUA framework not available")
        return False
    
    try:
        print("💻 Step 1: Starting VM with correct async context...")
        
        # This is the KEY FIX - use async context manager
        async with Computer(os_type="macos") as computer:
            print("✅ VM Started Successfully!")
            print("   🔍 You should see a VM window appear now")
            print("   ⏳ Waiting 3 seconds for VM to fully initialize...")
            await asyncio.sleep(3)
            
            print("\n📸 Step 2: Testing screenshot capture...")
            screenshot = await computer.interface.screenshot()
            print(f"✅ Screenshot captured: {len(screenshot)} bytes")
            
            # Save screenshot for inspection
            screenshot_path = "debug_vm_screenshot.png"
            with open(screenshot_path, "wb") as f:
                f.write(screenshot)
            print(f"💾 Screenshot saved as {screenshot_path}")
            
            print("\n🤖 Step 3: Creating CUA Agent...")
            agent = ComputerAgent(
                computer=computer,
                loop=AgentLoop.OMNI,  # Use OMNI loop for compatibility
                model=LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
            )
            print("✅ Agent created successfully")
            
            print("\n🎯 Step 4: Testing agent interaction...")
            print("   Task: Take a screenshot and describe what you see")
            
            # Simple agent task to test if it can see the desktop
            task = "Take a screenshot and describe what you see on the desktop"
            
            try:
                async for response in agent.run(task):
                    print(f"   🤖 Agent response: {response}")
                    break  # Just get the first response
                print("✅ Agent interaction successful")
            except Exception as e:
                print(f"⚠️ Agent interaction failed: {e}")
                print("   This might be expected if the desktop is blank")
            
            print("\n⏳ Waiting 2 seconds before shutdown...")
            await asyncio.sleep(2)
            
        print("\n✅ VM shutdown complete")
        print("\n🎉 VM Lifecycle Test Results:")
        print("   ✅ VM started successfully")
        print("   ✅ Screenshot captured")
        print("   ✅ Agent created")
        print("   ✅ VM shut down cleanly")
        print(f"\n📁 Check the screenshot: {screenshot_path}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ VM lifecycle test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the VM lifecycle verification test."""
    print("🔍 CUA VM Lifecycle Verification")
    print("=" * 60)
    print("This test verifies that we can properly start and interact with a CUA VM.")
    print("If this test fails, it means our fundamental VM setup is broken.")
    print()
    
    success = await test_vm_lifecycle()
    
    if success:
        print("\n🎉 SUCCESS: VM lifecycle is working correctly!")
        print("✅ You should have seen a VM window during the test")
        print("✅ The screenshot file should show the VM desktop")
        print("\nNext steps:")
        print("1. Examine the screenshot to verify it's not black")
        print("2. If the screenshot shows a desktop, the VM is working")
        print("3. If the screenshot is black, we have a display issue")
    else:
        print("\n❌ FAILURE: VM lifecycle is not working")
        print("This confirms our suspicion that VMs aren't starting properly")
        print("\nTroubleshooting:")
        print("1. Check if LUME is installed and running")
        print("2. Verify macOS virtualization permissions")
        print("3. Check system resources (memory, CPU)")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
