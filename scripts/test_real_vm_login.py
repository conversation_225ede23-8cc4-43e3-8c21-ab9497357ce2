#!/usr/bin/env python3
"""REAL VM Login Test - This test MUST FAIL until we can actually log in.

This test verifies that we can:
1. Start a VM
2. Actually log in (not just see a login screen)
3. Interact with the desktop (open apps, click things)
4. Prove the agent can see and interact with actual desktop elements

This test SHOULD FAIL until we solve the login problem.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.cua_session_manager import CUASessionManager


async def test_real_vm_interaction():
    """Test that we can actually log in and use the VM desktop.
    
    This test MUST FAIL until we can:
    - Log into the VM (not just see login screen)
    - See desktop elements (dock, menu bar, apps)
    - Actually interact with the desktop
    """
    print("🧪 REAL VM Login Test")
    print("=" * 50)
    print("This test MUST FAIL until we can actually log in to the VM")
    print("and interact with the desktop, not just see a login screen.")
    print()
    
    try:
        async with CUASessionManager() as session:
            print("✅ VM started (but probably at login screen)")
            
            # Take initial screenshot
            screenshot = await session.take_screenshot()
            print(f"📸 Initial screenshot: {len(screenshot)} bytes")
            
            # Test 1: Try to detect if we're at login screen vs desktop
            print("\n🔍 Test 1: Checking if we're at desktop or login screen...")
            response = await session.run_agent_task(
                "Look at the screen. Are you seeing a macOS desktop with dock and menu bar, "
                "or are you seeing a login screen? Be specific about what you see."
            )
            print(f"Agent sees: {response}")
            
            # If we see login screen, this test should FAIL
            if "login" in response.lower() or "sign in" in response.lower() or "password" in response.lower():
                print("❌ EXPECTED FAILURE: VM is at login screen, not desktop")
                print("   We need to solve the login problem before this test can pass")
                return False
            
            # Test 2: Try to interact with desktop elements
            print("\n🔍 Test 2: Trying to interact with desktop elements...")
            response = await session.run_agent_task(
                "Click on the Finder icon in the dock to open Finder. "
                "Then tell me if Finder opened successfully."
            )
            print(f"Finder interaction: {response}")
            
            # Test 3: Try to open an application
            print("\n🔍 Test 3: Trying to open an application...")
            response = await session.run_agent_task(
                "Open Safari by clicking on it in the dock or using Spotlight search. "
                "Tell me if Safari opened and you can see a browser window."
            )
            print(f"Safari interaction: {response}")
            
            # Test 4: Verify we can see actual UI elements
            print("\n🔍 Test 4: Checking for actual UI elements...")
            response = await session.run_agent_task(
                "Describe what you see on the screen. List specific UI elements like: "
                "menu bar, dock, open windows, buttons, text fields. "
                "If you only see a black screen or login screen, say so."
            )
            print(f"UI elements: {response}")
            
            # If we got this far and can see/interact with desktop, test passes
            if any(word in response.lower() for word in ["dock", "menu bar", "finder", "safari", "window"]):
                print("\n🎉 SUCCESS: We can actually interact with the VM desktop!")
                return True
            else:
                print("\n❌ FAILURE: Cannot interact with desktop - still at login or black screen")
                return False
                
    except Exception as e:
        print(f"\n❌ FAILURE: Exception during test: {e}")
        return False


async def test_login_with_password():
    """Test attempting to log in with a password.
    
    This test tries to actually log in to the VM.
    """
    print("\n🧪 Login Attempt Test")
    print("=" * 50)
    
    try:
        async with CUASessionManager() as session:
            # Try to log in with common passwords
            passwords_to_try = ["lume", "admin", "password", ""]
            
            for password in passwords_to_try:
                print(f"\n🔐 Trying to log in with password: '{password}'")
                
                response = await session.run_agent_task(
                    f"I see a login screen. Try to log in by: "
                    f"1. Click on the password field "
                    f"2. Type the password '{password}' "
                    f"3. Press Enter or click the login button "
                    f"4. Tell me if login was successful or failed"
                )
                print(f"Login attempt result: {response}")
                
                # Check if login was successful
                if "success" in response.lower() or "desktop" in response.lower():
                    print(f"✅ Login successful with password: '{password}'")
                    return True
                    
            print("❌ All login attempts failed")
            return False
            
    except Exception as e:
        print(f"❌ Login test failed with exception: {e}")
        return False


async def main():
    """Run the real VM tests that should fail until login works."""
    print("🚀 REAL VM INTERACTION TESTS")
    print("=" * 60)
    print("These tests are designed to FAIL until we solve the login problem.")
    print("They test actual VM interaction, not just 'VM started successfully'.")
    print()
    
    # Test 1: Real interaction test
    interaction_success = await test_real_vm_interaction()
    
    # Test 2: Login attempt test
    login_success = await test_login_with_password()
    
    print("\n" + "=" * 60)
    print("🏁 REAL TEST RESULTS:")
    print(f"   VM Interaction Test: {'PASS' if interaction_success else 'FAIL (EXPECTED)'}")
    print(f"   Login Attempt Test: {'PASS' if login_success else 'FAIL (EXPECTED)'}")
    
    if interaction_success and login_success:
        print("\n🎉 AMAZING! We can actually use the VM!")
        print("   Now we can build real automation on top of this.")
        return True
    else:
        print("\n❌ EXPECTED FAILURE: Cannot actually use the VM yet")
        print("   Next steps:")
        print("   1. Figure out how to log into the VM automatically")
        print("   2. Or configure VM to auto-login")
        print("   3. Or find the correct password")
        print("   4. Then re-run this test until it passes")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    # Exit with failure code until we can actually use the VM
    sys.exit(0 if success else 1)
