#!/usr/bin/env python3
"""Test script for CUASessionManager.

This script tests the CUASessionManager to ensure it properly manages
VM lifecycle and provides reliable access to Computer and Agent instances.
"""

import asyncio
import sys
import os

# Add src to path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.cua_session_manager import CUASessionManager, run_cua_task


async def test_basic_session_lifecycle():
    """Test basic session manager lifecycle."""
    print("🧪 Test 1: Basic Session Lifecycle")
    print("=" * 50)
    
    try:
        async with CUASessionManager() as session:
            print("✅ Session started successfully")
            
            # Test session is active
            assert session.is_active, "Session should be active"
            print("✅ Session is active")
            
            # Test computer access
            computer = session.get_computer()
            assert computer is not None, "Computer should not be None"
            print("✅ Computer instance obtained")
            
            # Test screenshot (proves VM is running)
            screenshot = await session.take_screenshot()
            assert len(screenshot) > 1000, f"Screenshot too small: {len(screenshot)} bytes"
            print(f"✅ Screenshot captured: {len(screenshot)} bytes")
            
            # Test agent creation
            agent = session.get_agent()
            assert agent is not None, "Agent should not be None"
            print("✅ Agent created successfully")
            
            # Test agent reuse
            agent2 = session.get_agent()
            assert agent is agent2, "Agent should be reused"
            print("✅ Agent reuse confirmed")
        
        # Test session is no longer active
        assert not session.is_active, "Session should not be active after exit"
        print("✅ Session properly deactivated after exit")
        
        print("\n🎉 Test 1 PASSED: Basic session lifecycle works correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test 1 FAILED: {e}")
        return False


async def test_error_handling():
    """Test error handling outside session context."""
    print("\n🧪 Test 2: Error Handling")
    print("=" * 50)
    
    try:
        session = CUASessionManager()
        
        # Should not be active before entering context
        assert not session.is_active, "Session should not be active initially"
        print("✅ Session inactive before context")
        
        # Should raise error when accessing outside context
        try:
            session.get_computer()
            print("❌ Should have raised RuntimeError")
            return False
        except RuntimeError as e:
            if "Session not active" in str(e):
                print("✅ Proper error when accessing computer outside context")
            else:
                print(f"❌ Wrong error message: {e}")
                return False
        
        try:
            session.get_agent()
            print("❌ Should have raised RuntimeError")
            return False
        except RuntimeError as e:
            if "Session not active" in str(e):
                print("✅ Proper error when accessing agent outside context")
            else:
                print(f"❌ Wrong error message: {e}")
                return False
        
        print("\n🎉 Test 2 PASSED: Error handling works correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test 2 FAILED: {e}")
        return False


async def test_agent_interaction():
    """Test actual agent interaction."""
    print("\n🧪 Test 3: Agent Interaction")
    print("=" * 50)
    
    try:
        async with CUASessionManager() as session:
            # Test convenience method
            response = await session.run_agent_task("Take a screenshot and describe what you see")
            assert isinstance(response, str), f"Response should be string, got {type(response)}"
            assert len(response) > 0, "Response should not be empty"
            print(f"✅ Agent task completed: {response[:100]}...")
        
        print("\n🎉 Test 3 PASSED: Agent interaction works correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test 3 FAILED: {e}")
        return False


async def test_convenience_function():
    """Test the standalone convenience function."""
    print("\n🧪 Test 4: Convenience Function")
    print("=" * 50)
    
    try:
        response = await run_cua_task("Take a screenshot and describe what you see")
        assert isinstance(response, str), f"Response should be string, got {type(response)}"
        assert len(response) > 0, "Response should not be empty"
        print(f"✅ Convenience function works: {response[:100]}...")
        
        print("\n🎉 Test 4 PASSED: Convenience function works correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test 4 FAILED: {e}")
        return False


async def test_different_providers():
    """Test different AI providers."""
    print("\n🧪 Test 5: Different Providers")
    print("=" * 50)
    
    try:
        # Test OpenAI provider
        async with CUASessionManager(provider="openai", model="gpt-4o") as session:
            agent = session.get_agent()
            assert agent is not None, "OpenAI agent should not be None"
            print("✅ OpenAI provider works")
        
        # Test Anthropic provider
        async with CUASessionManager(provider="anthropic", model="claude-3-5-sonnet-20241022") as session:
            agent = session.get_agent()
            assert agent is not None, "Anthropic agent should not be None"
            print("✅ Anthropic provider works")
        
        # Test invalid provider
        try:
            async with CUASessionManager(provider="invalid_provider") as session:
                session.get_agent()
                print("❌ Should have raised ValueError")
                return False
        except ValueError as e:
            if "Unsupported provider" in str(e):
                print("✅ Proper error for invalid provider")
            else:
                print(f"❌ Wrong error message: {e}")
                return False
        
        print("\n🎉 Test 5 PASSED: Provider handling works correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test 5 FAILED: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 CUASessionManager Test Suite")
    print("=" * 60)
    print("Testing the CUASessionManager implementation...")
    print()
    
    tests = [
        test_basic_session_lifecycle,
        test_error_handling,
        test_agent_interaction,
        test_convenience_function,
        test_different_providers,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if await test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"\n❌ Test failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! CUASessionManager is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
