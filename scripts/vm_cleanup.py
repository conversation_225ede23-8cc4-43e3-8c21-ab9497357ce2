#!/usr/bin/env python3
"""VM Cleanup Utility - Kill any running VMs before starting tests.

This ensures we start with a clean slate and avoid conflicts
from previous test runs or stuck VM processes.
"""

import asyncio
import subprocess
import sys
import json


async def cleanup_lume_vms():
    """Clean up any running LUME VMs."""
    print("🧹 Cleaning up LUME VMs...")
    
    try:
        # List all VMs
        result = subprocess.run(
            ["lume", "ls"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode != 0:
            print(f"❌ Failed to list VMs: {result.stderr}")
            return False
        
        print("📋 Current VM status:")
        print(result.stdout)
        
        # Parse the output to find running VMs
        lines = result.stdout.strip().split('\n')
        running_vms = []
        
        for line in lines[1:]:  # Skip header
            if line.strip() and 'running' in line.lower():
                # Extract VM name (first column)
                vm_name = line.split()[0]
                running_vms.append(vm_name)
        
        if not running_vms:
            print("✅ No running VMs found")
            return True
        
        print(f"🔄 Found {len(running_vms)} running VM(s): {running_vms}")
        
        # Stop each running VM
        for vm_name in running_vms:
            print(f"🛑 Stopping VM: {vm_name}")
            try:
                stop_result = subprocess.run(
                    ["lume", "stop", vm_name],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if stop_result.returncode == 0:
                    print(f"✅ Stopped VM: {vm_name}")
                else:
                    print(f"⚠️ Failed to stop VM {vm_name}: {stop_result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"⚠️ Timeout stopping VM: {vm_name}")
            except Exception as e:
                print(f"❌ Error stopping VM {vm_name}: {e}")
        
        # Wait a moment for VMs to fully stop
        print("⏳ Waiting for VMs to fully stop...")
        await asyncio.sleep(3)
        
        # Verify all VMs are stopped
        verify_result = subprocess.run(
            ["lume", "ls"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if verify_result.returncode == 0:
            print("📋 Final VM status:")
            print(verify_result.stdout)
            
            # Check if any VMs are still running
            still_running = []
            for line in verify_result.stdout.strip().split('\n')[1:]:
                if line.strip() and 'running' in line.lower():
                    vm_name = line.split()[0]
                    still_running.append(vm_name)
            
            if still_running:
                print(f"⚠️ Warning: {len(still_running)} VM(s) still running: {still_running}")
                return False
            else:
                print("✅ All VMs successfully stopped")
                return True
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ Timeout while listing VMs")
        return False
    except Exception as e:
        print(f"❌ Error during VM cleanup: {e}")
        return False


async def cleanup_background_processes():
    """Clean up any background processes that might interfere."""
    print("\n🧹 Cleaning up background processes...")
    
    try:
        # Kill any hanging Python processes related to CUA
        print("🔍 Looking for CUA-related Python processes...")
        
        ps_result = subprocess.run(
            ["ps", "aux"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if ps_result.returncode == 0:
            cua_processes = []
            for line in ps_result.stdout.split('\n'):
                if ('python' in line.lower() and 
                    ('cua' in line.lower() or 'computer' in line.lower() or 'agent' in line.lower()) and
                    'grep' not in line.lower()):
                    cua_processes.append(line.strip())
            
            if cua_processes:
                print(f"⚠️ Found {len(cua_processes)} potentially related processes:")
                for proc in cua_processes:
                    print(f"   {proc}")
                print("   (Not automatically killing - manual review recommended)")
            else:
                print("✅ No CUA-related processes found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking background processes: {e}")
        return False


async def full_cleanup():
    """Perform full cleanup of VMs and processes."""
    print("🚀 FULL VM CLEANUP")
    print("=" * 50)
    
    success = True
    
    # Clean up LUME VMs
    if not await cleanup_lume_vms():
        success = False
    
    # Check background processes
    if not await cleanup_background_processes():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CLEANUP COMPLETE! Ready for fresh VM tests.")
    else:
        print("⚠️ CLEANUP HAD ISSUES. Check logs above.")
    
    return success


# Convenience function for other scripts to import
async def ensure_clean_vm_state():
    """Ensure clean VM state before starting tests. Returns True if successful."""
    return await cleanup_lume_vms()


if __name__ == "__main__":
    success = asyncio.run(full_cleanup())
    sys.exit(0 if success else 1)
