#!/usr/bin/env python3
"""
CUA Agent - A practical computer-use agent for automation tasks
"""

import asyncio
import os
import sys
import argparse
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
import json
from datetime import datetime

from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

# For now, let's create a simple agent that can work without the full CUA framework
# We'll use basic automation tools and AI providers directly

# Load environment variables
load_dotenv()

console = Console()

class SimpleAgent:
    """Simple automation agent that can perform basic tasks."""

    def __init__(self, config_path: str = "config/settings.yaml"):
        """Initialize the Simple Agent."""
        self.config = self.load_config(config_path)
        self.output_dir = Path(self.config.get("output", {}).get("base_directory", "./output"))
        self.create_output_dirs()

        # Initialize AI client
        self.ai_client = self.setup_ai_client()
        
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        config_file = Path(config_path)
        if config_file.exists():
            with open(config_file) as f:
                return yaml.safe_load(f)
        else:
            # Return default configuration
            return {
                "vm": {
                    "display": "1024x768",
                    "memory": "8GB",
                    "cpu": "4",
                    "os_type": "macos",
                    "ephemeral": False,
                    "provider_type": "lume"
                },
                "ai": {
                    "default_provider": "anthropic",
                    "default_model": "claude-3-5-sonnet-20241022",
                    "max_tokens": 4096,
                    "temperature": 0.1
                },
                "safety": {
                    "require_confirmation": True,
                    "auto_screenshot": True,
                    "max_actions_per_task": 50
                },
                "output": {
                    "base_directory": "./output"
                }
            }
    
    def create_output_dirs(self):
        """Create output directories."""
        dirs = ["screenshots", "logs", "results"]
        for dir_name in dirs:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)

    def setup_ai_client(self):
        """Set up AI client based on configuration."""
        ai_config = self.config.get("ai", {})
        provider = ai_config.get("default_provider", "anthropic")

        if provider == "anthropic":
            try:
                import anthropic
                api_key = os.getenv("ANTHROPIC_API_KEY")
                if not api_key:
                    console.print("[yellow]Warning: ANTHROPIC_API_KEY not found in environment[/yellow]")
                    return None
                return anthropic.Anthropic(api_key=api_key)
            except ImportError:
                console.print("[red]Anthropic package not installed[/red]")
                return None
        elif provider == "openai":
            try:
                import openai
                api_key = os.getenv("OPENAI_API_KEY")
                if not api_key:
                    console.print("[yellow]Warning: OPENAI_API_KEY not found in environment[/yellow]")
                    return None
                return openai.OpenAI(api_key=api_key)
            except ImportError:
                console.print("[red]OpenAI package not installed[/red]")
                return None

        return None

    async def initialize(self):
        """Initialize the agent."""
        console.print("[bold blue]🚀 Initializing Simple Agent...[/bold blue]")

        if self.ai_client:
            console.print("[bold green]✅ AI client initialized successfully![/bold green]")
        else:
            console.print("[bold yellow]⚠️  No AI client available. Some features may be limited.[/bold yellow]")

        console.print("[bold green]✅ Agent ready![/bold green]")
        
    async def run_task(self, task_name: str, **kwargs):
        """Run a specific task."""
        console.print(f"[bold yellow]🎯 Running task: {task_name}[/bold yellow]")
        
        # Safety check
        if self.config.get("safety", {}).get("require_confirmation", True):
            if not Confirm.ask(f"Are you sure you want to run task '{task_name}'?"):
                console.print("[yellow]Task cancelled by user.[/yellow]")
                return
        
        try:
            # Create task prompt based on task name and parameters
            prompt = self.create_task_prompt(task_name, **kwargs)
            
            # Execute the task
            result = await self.agent.run(prompt)
            
            # Save results
            await self.save_results(task_name, result, kwargs)
            
            console.print(f"[bold green]✅ Task '{task_name}' completed successfully![/bold green]")
            return result
            
        except Exception as e:
            console.print(f"[bold red]❌ Task '{task_name}' failed: {e}[/bold red]")
            raise
    
    def create_task_prompt(self, task_name: str, **kwargs) -> str:
        """Create a prompt for the specified task."""
        
        if task_name == "web_research":
            query = kwargs.get("query", "")
            sources = kwargs.get("sources", 3)
            return f"""
            I need you to research the topic: "{query}"
            
            Please:
            1. Open a web browser
            2. Search for "{query}" using Google or another search engine
            3. Visit the top {sources} relevant websites
            4. Extract key information from each source
            5. Summarize the findings
            
            For each source, please note:
            - Website URL
            - Title of the page/article
            - Key points and information
            - Publication date if available
            - Author/source credibility
            
            Finally, provide a comprehensive summary of your research findings.
            """
        
        elif task_name == "organize_files":
            path = kwargs.get("path", "~/Downloads")
            method = kwargs.get("method", "type")
            return f"""
            I need you to organize files in this directory: {path}
            
            Organization method: {method}
            
            Please:
            1. Open Finder and navigate to {path}
            2. Analyze the current file structure and contents
            3. Take a screenshot of the current state
            
            Based on the organization method '{method}':
            
            If method is 'type':
            - Create folders for different file types (Documents, Images, Videos, Audio, Archives, etc.)
            - Move files into appropriate folders based on their extensions
            
            If method is 'date':
            - Create folders based on file creation/modification dates (YYYY-MM format)
            - Move files into date-based folders
            
            If method is 'size':
            - Create folders for different size ranges (Small <1MB, Medium 1-10MB, Large >10MB)
            - Move files based on their sizes
            
            4. Create the necessary folders
            5. Move files to their new locations
            6. Take a screenshot of the organized structure
            7. Provide a summary of what was organized
            
            Be careful not to move system files or hidden files.
            """
        
        elif task_name == "setup_project":
            name = kwargs.get("name", "new-project")
            project_type = kwargs.get("type", "python")
            return f"""
            I need you to set up a new {project_type} project named "{name}".
            
            Please:
            1. Open Terminal
            2. Navigate to a suitable directory (like ~/workspace or ~/projects)
            3. Create a new directory for the project: {name}
            4. Set up the project structure based on the type: {project_type}
            
            For Python projects:
            - Create main.py, requirements.txt, README.md
            - Set up virtual environment
            - Create basic project structure
            
            For JavaScript/React projects:
            - Use appropriate scaffolding tools (create-react-app, etc.)
            - Set up package.json and basic structure
            
            5. Initialize git repository if requested
            6. Create initial commit
            7. Open the project in the default editor
            
            Provide a summary of what was created and next steps.
            """
        
        else:
            return f"""
            I need you to help me with the task: {task_name}
            
            Parameters provided: {kwargs}
            
            Please analyze what needs to be done and execute the appropriate actions.
            Take screenshots as needed and provide a detailed summary of your actions.
            """
    
    async def save_results(self, task_name: str, result: Any, params: Dict[str, Any]):
        """Save task results to output directory."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_path = self.output_dir / "results" / f"{task_name}_{timestamp}.json"
        
        with open(result_path, 'w') as f:
            json.dump({
                "task": task_name,
                "timestamp": timestamp,
                "parameters": params,
                "result": str(result)  # Convert to string for JSON serialization
            }, f, indent=2, default=str)
        
        console.print(f"[dim]Results saved: {result_path}[/dim]")
    
    async def interactive_mode(self):
        """Run the agent in interactive mode."""
        console.print(Panel.fit(
            "[bold blue]🤖 CUA Agent Interactive Mode[/bold blue]\n"
            "Type 'help' for available commands, 'quit' to exit.",
            title="Welcome"
        ))
        
        while True:
            try:
                command = Prompt.ask("\n[bold cyan]Agent[/bold cyan]")
                
                if command.lower() in ["quit", "exit", "q"]:
                    break
                elif command.lower() == "help":
                    self.show_help()
                elif command.lower() == "status":
                    await self.show_status()
                elif command.lower() == "tasks":
                    self.show_available_tasks()
                elif command.startswith("run "):
                    task_name = command[4:].strip()
                    await self.run_interactive_task(task_name)
                else:
                    console.print("[yellow]Unknown command. Type 'help' for available commands.[/yellow]")
                    
            except KeyboardInterrupt:
                console.print("\n[yellow]Use 'quit' to exit gracefully.[/yellow]")
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
        
        console.print("[bold blue]👋 Goodbye![/bold blue]")
    
    def show_help(self):
        """Show help information."""
        table = Table(title="Available Commands")
        table.add_column("Command", style="cyan")
        table.add_column("Description", style="white")
        
        table.add_row("help", "Show this help message")
        table.add_row("status", "Show agent status")
        table.add_row("tasks", "List available tasks")
        table.add_row("run <task>", "Run a specific task")
        table.add_row("quit", "Exit the agent")
        
        console.print(table)
    
    async def show_status(self):
        """Show current agent status."""
        status_table = Table(title="Agent Status")
        status_table.add_column("Component", style="cyan")
        status_table.add_column("Status", style="white")
        
        status_table.add_row("Computer", "✅ Connected" if self.computer else "❌ Not connected")
        status_table.add_row("Agent", "✅ Ready" if self.agent else "❌ Not initialized")
        status_table.add_row("Output Dir", str(self.output_dir))
        status_table.add_row("CUA Path", str(cua_path))
        
        console.print(status_table)
    
    def show_available_tasks(self):
        """Show available tasks."""
        table = Table(title="Available Tasks")
        table.add_column("Task", style="cyan")
        table.add_column("Description", style="white")
        
        tasks = [
            ("web_research", "Research a topic using web search"),
            ("organize_files", "Organize files in a directory"),
            ("setup_project", "Set up a new development project"),
            ("web_scraping", "Extract data from websites"),
            ("backup_files", "Create backups of files"),
            ("code_review", "Review code for issues"),
            ("system_monitor", "Monitor system resources")
        ]
        
        for task_name, description in tasks:
            table.add_row(task_name, description)
        
        console.print(table)
    
    async def run_interactive_task(self, task_name: str):
        """Run a task in interactive mode with parameter prompts."""
        console.print(f"[yellow]Running task: {task_name}[/yellow]")
        
        # Simple parameter collection for common tasks
        params = {}
        
        if task_name == "web_research":
            params["query"] = Prompt.ask("Enter search query")
            params["sources"] = int(Prompt.ask("Number of sources", default="3"))
        elif task_name == "organize_files":
            params["path"] = Prompt.ask("Directory path", default="~/Downloads")
            params["method"] = Prompt.ask("Organization method", choices=["type", "date", "size"], default="type")
        elif task_name == "setup_project":
            params["name"] = Prompt.ask("Project name")
            params["type"] = Prompt.ask("Project type", choices=["python", "javascript", "react"], default="python")
        
        await self.run_task(task_name, **params)

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="CUA Agent - Computer Use Automation")
    parser.add_argument("--task", help="Task to run")
    parser.add_argument("--interactive", "-i", action="store_true", help="Run in interactive mode")
    parser.add_argument("--config", default="config/settings.yaml", help="Configuration file")
    
    # Task-specific arguments
    parser.add_argument("--query", help="Search query for web research")
    parser.add_argument("--url", help="URL for web scraping")
    parser.add_argument("--path", help="File/directory path")
    parser.add_argument("--name", help="Project name")
    parser.add_argument("--type", help="Project type")
    parser.add_argument("--sources", type=int, default=3, help="Number of sources for research")
    parser.add_argument("--method", help="Organization method for file tasks")
    
    args = parser.parse_args()
    
    # Initialize agent
    agent = CUAAgent(args.config)
    await agent.initialize()
    
    if args.interactive:
        await agent.interactive_mode()
    elif args.task:
        # Extract task parameters from args
        task_params = {}
        if args.query:
            task_params["query"] = args.query
        if args.url:
            task_params["url"] = args.url
        if args.path:
            task_params["path"] = args.path
        if args.name:
            task_params["name"] = args.name
        if args.type:
            task_params["type"] = args.type
        if args.sources:
            task_params["sources"] = args.sources
        if args.method:
            task_params["method"] = args.method
        
        await agent.run_task(args.task, **task_params)
    else:
        console.print("[yellow]No task specified. Use --task or --interactive mode.[/yellow]")
        console.print("[yellow]Run with --help for usage information.[/yellow]")

if __name__ == "__main__":
    asyncio.run(main())
