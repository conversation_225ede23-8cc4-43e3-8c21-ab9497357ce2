#!/usr/bin/env python3
"""
Real CUA Automation - Actual Web Automation with LUME VM

This script demonstrates REAL CUA automation using the running LUME VM.
No mocking - actual browser control and web scraping.
"""

import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


async def test_real_cua_automation():
    """Test real CUA automation with the running LUME VM."""
    print("🚀 Real CUA Automation Test")
    print("=" * 50)
    
    try:
        from computer import Computer
        from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
        
        print("✓ CUA modules imported")
        
        # Step 1: Connect to the running LUME VM
        print("\n1. Connecting to LUME VM...")
        computer = Computer(os_type="macos")
        await computer.run()
        print("✓ Connected to macOS VM")
        
        # Step 2: Take a screenshot to verify connection
        print("\n2. Taking screenshot...")
        screenshot_bytes = await computer.interface.screenshot()
        print(f"✓ Screenshot captured ({len(screenshot_bytes)} bytes)")
        
        # Step 3: Test basic computer control
        print("\n3. Testing basic computer control...")
        
        # Get screen size
        screen_size = await computer.interface.get_screen_size()
        print(f"✓ Screen size: {screen_size}")

        # Move cursor to center
        center_x, center_y = screen_size['width'] // 2, screen_size['height'] // 2
        await computer.interface.move_cursor(center_x, center_y)
        print(f"✓ Moved cursor to center ({center_x}, {center_y})")
        
        # Step 4: Open Safari and navigate to a website
        print("\n4. Opening Safari...")
        
        # Open Safari using Spotlight
        await computer.interface.hotkey("command", "space")
        await asyncio.sleep(1)
        await computer.interface.type_text("Safari")
        await asyncio.sleep(1)
        await computer.interface.press_key("return")
        await asyncio.sleep(3)
        print("✓ Safari opened")
        
        # Navigate to a simple website
        print("\n5. Navigating to website...")
        await computer.interface.hotkey("command", "l")  # Focus address bar
        await asyncio.sleep(1)
        await computer.interface.type_text("https://httpbin.org/html")
        await computer.interface.press_key("return")
        await asyncio.sleep(5)
        print("✓ Navigated to test website")
        
        # Step 5: Take another screenshot to show the webpage
        print("\n6. Capturing webpage screenshot...")
        webpage_screenshot = await computer.interface.screenshot()
        print(f"✓ Webpage screenshot captured ({len(webpage_screenshot)} bytes)")
        
        # Step 6: Test CUA Agent for natural language automation
        print("\n7. Testing CUA Agent...")
        
        # Create agent with OpenAI
        llm = LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
        agent = ComputerAgent(
            computer=computer,
            loop=AgentLoop.OPENAI,
            model=llm
        )
        print("✓ CUA Agent created")
        
        # Use agent to perform a simple task
        print("\n8. Running agent task...")
        task = "Take a screenshot of the current webpage"
        
        async for result in agent.run(task):
            print(f"Agent result: {result}")
            break  # Just get the first result for testing
        
        print("✓ Agent task completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up - close Safari
        try:
            print("\n9. Cleaning up...")
            await computer.interface.hotkey("command", "q")  # Quit Safari
            print("✓ Safari closed")
        except:
            pass


async def main():
    """Run the real CUA automation test."""
    print("🔍 Real CUA Automation with LUME VM")
    print("This will actually control the macOS VM and perform web automation")
    print()
    
    # Check if VM is running
    import subprocess
    result = subprocess.run(["lume", "ls"], capture_output=True, text=True)
    if "running" not in result.stdout:
        print("❌ LUME VM is not running!")
        print("Please run: lume run macos-sequoia-cua:latest")
        return False
    
    print("✓ LUME VM is running")
    
    # Run the automation test
    success = await test_real_cua_automation()
    
    if success:
        print("\n🎉 Real CUA automation test completed successfully!")
        print("✓ VM connection working")
        print("✓ Computer interface working")
        print("✓ Browser automation working")
        print("✓ CUA Agent working")
        print("\nReady for job collection automation!")
    else:
        print("\n❌ Real CUA automation test failed")
        print("Check the error messages above")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
