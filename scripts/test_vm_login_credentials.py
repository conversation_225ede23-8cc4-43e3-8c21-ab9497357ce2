#!/usr/bin/env python3
"""Test VM login with discovered credentials.

Based on LUME documentation, the default credentials should be:
- Username: lume
- Password: lume

And auto-login should be enabled, but we're seeing a login screen.
Let's test if the agent can actually log in with these credentials.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.cua_session_manager import CUASessionManager


async def test_vm_login_with_credentials():
    """Test logging into the VM with the documented default credentials."""
    print("🔐 Testing VM Login with Default Credentials")
    print("=" * 60)
    print("According to LUME docs: username='lume', password='lume'")
    print("And auto-login should be enabled, but we see a login screen.")
    print()
    
    try:
        async with CUASessionManager() as session:
            print("✅ VM started and session active")
            
            # Take initial screenshot to see current state
            screenshot = await session.take_screenshot()
            print(f"📸 Initial screenshot: {len(screenshot)} bytes")
            
            # Test 1: Ask agent to identify what's on screen
            print("\n🔍 Test 1: What does the agent see?")
            response = await session.run_agent_task(
                "Look at the screen carefully. Describe exactly what you see. "
                "Is this a login screen? A desktop? Something else? "
                "Be very specific about any UI elements, text, or buttons you can see."
            )
            print(f"Agent sees: {response}")
            
            # Test 2: Try to log in with the documented credentials
            print("\n🔐 Test 2: Attempting login with lume/lume credentials")
            login_response = await session.run_agent_task(
                "I need you to log into this macOS system. The credentials are: "
                "Username: lume "
                "Password: lume "
                "Please: "
                "1. Look for a username field and click it if needed "
                "2. Type 'lume' as the username "
                "3. Look for a password field and click it "
                "4. Type 'lume' as the password "
                "5. Press Enter or click the login button "
                "6. Tell me if the login was successful and what you see after"
            )
            print(f"Login attempt result: {login_response}")
            
            # Test 3: Check what we see after login attempt
            print("\n📸 Test 3: Screenshot after login attempt")
            screenshot2 = await session.take_screenshot()
            print(f"Post-login screenshot: {len(screenshot2)} bytes")
            
            final_response = await session.run_agent_task(
                "Look at the screen now. Did the login work? "
                "Are you seeing a macOS desktop with dock and menu bar? "
                "Or are you still at a login screen? "
                "Describe exactly what you see."
            )
            print(f"Final state: {final_response}")
            
            # Determine success
            if any(word in final_response.lower() for word in ["desktop", "dock", "menu bar", "finder"]):
                print("\n🎉 SUCCESS: Login appears to have worked!")
                print("   The agent can now see and interact with the desktop.")
                return True
            else:
                print("\n❌ LOGIN FAILED: Still not seeing desktop")
                print("   Need to investigate further or try different approach.")
                return False
                
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        return False


async def main():
    """Run the login test."""
    print("🚀 VM LOGIN CREDENTIALS TEST")
    print("=" * 60)
    print("Testing the documented default credentials for LUME VMs")
    print()
    
    success = await test_vm_login_with_credentials()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CREDENTIALS WORK! The VM login issue is solved.")
        print("   Next steps:")
        print("   1. Update CUASessionManager to auto-login on startup")
        print("   2. Configure VM to prevent sleep after login")
        print("   3. Proceed with building the job collection system")
    else:
        print("❌ CREDENTIALS DON'T WORK or login process failed.")
        print("   Next steps:")
        print("   1. Check if auto-login is actually enabled in the VM")
        print("   2. Try different credentials or login methods")
        print("   3. Investigate VM image configuration")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
