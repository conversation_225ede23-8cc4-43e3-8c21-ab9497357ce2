#!/usr/bin/env python3
"""Test GPT-4o configuration with CUASessionManager.

Quick test to verify that GPT-4o works correctly with our setup.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.cua_session_manager import CUASessionManager
from vm_cleanup import ensure_clean_vm_state


async def test_gpt4o_configuration():
    """Test that GPT-4o works with our CUASessionManager."""
    print("🤖 Testing GPT-4o Configuration")
    print("=" * 50)
    
    try:
        # Clean up first
        print("🧹 Cleaning up existing VMs...")
        await ensure_clean_vm_state()
        
        print("\n🚀 Starting VM with GPT-4o...")
        async with CUASessionManager(model="gpt-4o") as session:
            print("✅ Session started successfully")
            
            # Test basic functionality
            print("\n📸 Testing screenshot capture...")
            screenshot = await session.take_screenshot()
            print(f"✅ Screenshot captured: {len(screenshot)} bytes")
            
            # Test agent creation and simple task
            print("\n🤖 Testing GPT-4o agent...")
            response = await session.run_agent_task(
                "Look at the screen and tell me what you see. Keep your response brief."
            )
            print(f"✅ Agent response: {response[:200]}...")

            print("\n🎉 GPT-4o configuration works!")
            return True

    except Exception as e:
        print(f"\n❌ GPT-4o test failed: {e}")
        return False


async def main():
    """Run the GPT-4o configuration test."""
    print("🚀 GPT-4O CONFIGURATION TEST")
    print("=" * 60)

    success = await test_gpt4o_configuration()

    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: GPT-4o configuration works correctly!")
        print("   The system is ready for CUA automation with GPT-4o.")
    else:
        print("❌ FAILURE: GPT-4o configuration has issues.")
        print("   Check the error above and fix before proceeding.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
