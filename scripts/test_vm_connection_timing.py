#!/usr/bin/env python3
"""Test VM connection timing to understand the disconnect issue.

The VM opens and is visible, but we're getting disconnected.
This suggests a timing issue with the Computer Server startup.
"""

import asyncio
import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from computer import Computer


async def test_vm_connection_timing():
    """Test the VM connection process step by step to identify where disconnection occurs."""
    print("🔍 VM Connection Timing Test")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        print("💻 Step 1: Creating Computer instance...")
        computer = Computer(os_type="macos")
        print(f"✅ Computer created ({time.time() - start_time:.1f}s)")
        
        print("\n🚀 Step 2: Starting VM (this takes ~30 seconds)...")
        step2_start = time.time()
        await computer.run()
        print(f"✅ VM started ({time.time() - step2_start:.1f}s)")
        
        print("\n📡 Step 3: Testing WebSocket connection...")
        step3_start = time.time()
        
        # Try to get IP first
        ip = await computer.get_ip()
        print(f"✅ Got VM IP: {ip} ({time.time() - step3_start:.1f}s)")
        
        # Test if Computer Server is responding
        print(f"\n🔌 Step 4: Testing Computer Server at {ip}:8000...")
        import aiohttp
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"http://{ip}:8000/", timeout=10) as response:
                    print(f"✅ Computer Server responding: {response.status}")
            except Exception as e:
                print(f"❌ Computer Server not responding: {e}")
        
        # Test screenshot capability
        print(f"\n📸 Step 5: Testing screenshot capture...")
        step5_start = time.time()
        screenshot = await computer.interface.screenshot()
        print(f"✅ Screenshot captured: {len(screenshot)} bytes ({time.time() - step5_start:.1f}s)")
        
        # Save screenshot for inspection
        with open("debug_connection_test.png", "wb") as f:
            f.write(screenshot)
        print("💾 Screenshot saved as debug_connection_test.png")
        
        print(f"\n🎉 SUCCESS: Full connection established in {time.time() - start_time:.1f}s total")
        
        # Keep connection alive for a bit to test stability
        print("\n⏱️  Step 6: Testing connection stability (30 seconds)...")
        for i in range(6):
            await asyncio.sleep(5)
            try:
                screenshot = await computer.interface.screenshot()
                print(f"   Connection stable at {(i+1)*5}s: {len(screenshot)} bytes")
            except Exception as e:
                print(f"   ❌ Connection lost at {(i+1)*5}s: {e}")
                return False
        
        print("✅ Connection remained stable for 30 seconds")
        
        # Clean shutdown
        print("\n🔄 Step 7: Clean shutdown...")
        await computer.stop()
        print("✅ Clean shutdown completed")
        
        return True
        
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"\n❌ Connection failed after {elapsed:.1f}s: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Try to clean up
        try:
            if 'computer' in locals():
                await computer.stop()
        except:
            pass
        
        return False


async def main():
    """Run the connection timing test."""
    print("🚀 VM CONNECTION TIMING TEST")
    print("=" * 60)
    print("Testing step-by-step to identify where disconnection occurs")
    print()
    
    success = await test_vm_connection_timing()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CONNECTION STABLE! No disconnection issues found.")
        print("   The VM connection works properly.")
    else:
        print("❌ CONNECTION ISSUES IDENTIFIED.")
        print("   Check the logs above to see where the failure occurred.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
