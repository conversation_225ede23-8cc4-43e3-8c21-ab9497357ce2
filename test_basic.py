#!/usr/bin/env python3
"""
Basic CUA Agent Test

This is the main test that demonstrates what the CUA agent can do.
All outputs go to the output/ directory for easy cleanup.
"""

import os
import sys
import asyncio
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError:
    CUA_AVAILABLE = False
    print("❌ CUA framework not available. Install with: pip install cua-agent[omni]")
    exit(1)

from src.job_collector.utils.session_recorder import SessionRecorder


async def test_basic_agent():
    """Test basic CUA agent functionality with clear output."""
    
    print("🚀 CUA Agent Basic Test")
    print("=" * 50)
    print("This test demonstrates:")
    print("- Agent initialization")
    print("- Simple task execution")
    print("- Session recording with video")
    print("- Centralized output in output/ directory")
    print()
    
    # Create output directory
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    test_name = f"basic_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"📹 Starting session: {test_name}")
    print(f"📁 All outputs will be saved to: output/")
    print()
    
    with SessionRecorder(session_name=test_name, record_video=True) as recorder:
        recorder.add_metadata("test_type", "basic_functionality")
        recorder.add_metadata("purpose", "Demonstrate core CUA agent capabilities")
        
        try:
            print("🔧 Initializing CUA Computer...")
            async with Computer() as computer:
                print("✅ Computer initialized")
                
                print("🤖 Creating CUA Agent...")
                agent = ComputerAgent(
                    computer=computer,
                    loop=AgentLoop.OMNI,
                    model=LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
                )
                print("✅ Agent created")
                
                recorder.record_agent_response("Agent initialized successfully", response_type="initialization")
                
                # Simple task: Take a screenshot and describe what's visible
                task_id = recorder.start_task("Take screenshot and describe desktop", "screenshot_analysis")
                
                task = "Take a screenshot of the current desktop and describe what you can see"
                print(f"\n🎯 Task: {task}")
                recorder.record_agent_response(f"Starting task: {task}", task_id, "task_start")
                
                print("🔄 Agent is working...")
                response_text = ""
                async for response in agent.run(task):
                    # Handle response safely
                    text_content = ""
                    if isinstance(response, dict):
                        text_value = response.get("text")
                        if isinstance(text_value, str):
                            text_content = text_value
                        elif text_value is not None:
                            text_content = str(text_value)
                        else:
                            text_content = str(response)
                    else:
                        text_content = str(response)
                    
                    response_text += text_content
                    if text_content.strip():
                        recorder.record_agent_response(text_content, task_id, "agent_output")
                        print(f"   📝 Agent: {text_content[:100]}{'...' if len(text_content) > 100 else ''}")
                
                recorder.end_task(task_id, "completed", "Screenshot analysis completed")
                
                print("\n🏁 Test completed successfully!")
                print(f"📊 Response length: {len(response_text)} characters")
                
        except Exception as e:
            recorder.record_error(f"Test failed: {str(e)}", error_type="test_failure")
            print(f"❌ Test failed: {e}")
            raise
    
    print("\n" + "=" * 50)
    print("✅ Basic test completed!")
    print(f"📁 Check output/sessions/{test_name}/ for:")
    print("   - session.json (detailed logs)")
    print("   - session_report.md (human-readable report)")
    print("   - session_recording_*.mp4 (video recording)")
    print()


if __name__ == "__main__":
    asyncio.run(test_basic_agent())
