#!/usr/bin/env python3
"""
Example usage of the Job Collector system (Phase 1 foundation).

This demonstrates how to use the core components that have been implemented.
Note: This is just the foundation - actual data source adapters will be 
implemented in Phase 2.
"""

from datetime import datetime
from job_collector import (
    JobListing, SearchParameters, CollectionResult,
    JobCollectorConfig, JobCollector
)
from job_collector.core.models import JobType, ExperienceLevel, RemoteType
from job_collector.collectors.base_analyzer import BaseResultsAnalyzer


def create_sample_jobs():
    """Create some sample job listings for demonstration."""
    jobs = [
        JobListing(
            id="job-001",
            title="Senior Python Developer",
            company="TechCorp Inc",
            platform="example",
            url="https://example.com/jobs/001",
            location="San Francisco, CA",
            remote_type=RemoteType.HYBRID,
            job_type=JobType.FULL_TIME,
            experience_level=ExperienceLevel.SENIOR_LEVEL,
            salary_min=120000,
            salary_max=160000,
            required_skills=["Python", "Django", "PostgreSQL"],
            preferred_skills=["AWS", "Docker", "Kubernetes"],
            description="Looking for an experienced Python developer to join our backend team. Must have experience with Django and PostgreSQL.",
            posted_date=datetime.now()
        ),
        JobListing(
            id="job-002",
            title="Frontend React Developer",
            company="StartupXYZ",
            platform="example",
            url="https://example.com/jobs/002",
            location="Remote",
            remote_type=RemoteType.REMOTE,
            job_type=JobType.FULL_TIME,
            experience_level=ExperienceLevel.MID_LEVEL,
            salary_min=90000,
            salary_max=130000,
            required_skills=["React", "JavaScript", "TypeScript"],
            preferred_skills=["Next.js", "GraphQL", "Jest"],
            description="Remote React developer position. We're looking for someone with strong JavaScript and TypeScript skills.",
            posted_date=datetime.now()
        ),
        JobListing(
            id="job-003",
            title="Data Scientist",
            company="DataCorp",
            platform="example",
            url="https://example.com/jobs/003",
            location="New York, NY",
            remote_type=RemoteType.ON_SITE,
            job_type=JobType.FULL_TIME,
            experience_level=ExperienceLevel.MID_LEVEL,
            salary_min=110000,
            salary_max=150000,
            required_skills=["Python", "Machine Learning", "SQL"],
            preferred_skills=["TensorFlow", "PyTorch", "Spark"],
            description="Data scientist role focusing on machine learning and predictive analytics. Python and SQL experience required.",
            posted_date=datetime.now()
        )
    ]
    return jobs


def demonstrate_configuration():
    """Demonstrate configuration system."""
    print("=== Configuration System Demo ===")
    
    # Create configuration
    config = JobCollectorConfig()
    config.log_level = "DEBUG"
    config.output_directory = "./demo_output"
    
    # Save configuration to file
    config.save_to_file("demo_config.yaml")
    print("✅ Configuration saved to demo_config.yaml")
    
    # Load configuration from file
    loaded_config = JobCollectorConfig.load_from_file("demo_config.yaml")
    print(f"✅ Configuration loaded: log_level = {loaded_config.log_level}")
    
    return config


def demonstrate_job_collector(config):
    """Demonstrate JobCollector functionality."""
    print("\n=== JobCollector Demo ===")
    
    # Initialize collector
    collector = JobCollector(config)
    print("✅ JobCollector initialized")
    
    # Check registered platforms (should be empty initially)
    platforms = collector.get_registered_platforms()
    print(f"✅ Registered platforms: {platforms}")
    
    # Get platform status
    status = collector.get_platform_status()
    print(f"✅ Platform status: {status}")
    
    return collector


def demonstrate_data_models():
    """Demonstrate data model functionality."""
    print("\n=== Data Models Demo ===")
    
    # Create sample jobs
    jobs = create_sample_jobs()
    print(f"✅ Created {len(jobs)} sample job listings")
    
    # Demonstrate serialization
    job_dict = jobs[0].to_dict()
    job_from_dict = JobListing.from_dict(job_dict)
    print("✅ Job serialization/deserialization working")
    
    # Create search parameters
    search_params = SearchParameters(
        keywords=["python", "developer"],
        location="San Francisco",
        job_types=[JobType.FULL_TIME],
        experience_levels=[ExperienceLevel.SENIOR_LEVEL, ExperienceLevel.MID_LEVEL],
        remote_types=[RemoteType.REMOTE, RemoteType.HYBRID],
        min_salary=100000,
        max_results=50
    )
    print("✅ SearchParameters created")
    
    # Create collection result
    result = CollectionResult(
        search_parameters=search_params,
        platform="example"
    )
    
    for job in jobs:
        result.add_job(job)
    
    print(f"✅ CollectionResult created with {result.total_collected} jobs")
    
    return jobs, search_params, result


def demonstrate_analyzer(jobs):
    """Demonstrate results analyzer functionality."""
    print("\n=== Results Analyzer Demo ===")
    
    analyzer = BaseResultsAnalyzer()
    
    # Test filtering
    python_jobs = analyzer.filter_results(jobs, {"keywords": ["python"]})
    print(f"✅ Filtered for Python jobs: {len(python_jobs)} found")
    
    remote_jobs = analyzer.filter_results(jobs, {"remote_types": [RemoteType.REMOTE]})
    print(f"✅ Filtered for remote jobs: {len(remote_jobs)} found")
    
    # Test ranking
    preferences = {
        "preferred_keywords": ["python"],
        "prefer_remote": True,
        "preferred_skills": ["Django", "AWS"]
    }
    ranked_jobs = analyzer.rank_results(jobs, preferences)
    print(f"✅ Ranked {len(ranked_jobs)} jobs by preferences")
    
    # Test duplicate detection
    duplicates = analyzer.detect_duplicates(jobs)
    print(f"✅ Duplicate detection: {len(duplicates)} duplicate groups found")
    
    # Test insights extraction
    insights = analyzer.extract_insights(jobs)
    print("✅ Insights extracted:")
    print(f"   - Total jobs: {insights['total_jobs']}")
    print(f"   - Unique companies: {insights['unique_companies']}")
    print(f"   - Job types: {insights.get('job_type_distribution', {})}")
    print(f"   - Remote types: {insights.get('remote_type_distribution', {})}")
    if 'salary_stats' in insights:
        salary_stats = insights['salary_stats']
        print(f"   - Salary range: ${salary_stats['min']:,.0f} - ${salary_stats['max']:,.0f}")
    
    # Test categorization
    categories = analyzer.categorize_jobs(jobs)
    print("✅ Job categorization:")
    for category, job_list in categories.items():
        print(f"   - {category}: {len(job_list)} jobs")
    
    return analyzer


def main():
    """Run the demonstration."""
    print("Job Collector System - Phase 1 Foundation Demo")
    print("=" * 60)
    
    try:
        # Demonstrate each component
        config = demonstrate_configuration()
        collector = demonstrate_job_collector(config)
        jobs, search_params, result = demonstrate_data_models()
        analyzer = demonstrate_analyzer(jobs)
        
        print("\n" + "=" * 60)
        print("🎉 Phase 1 Foundation Demo Complete!")
        print("\nAll core components are working correctly:")
        print("✅ Configuration system")
        print("✅ Data models and serialization")
        print("✅ JobCollector orchestrator")
        print("✅ Results analyzer")
        print("✅ Logging and utilities")
        print("\nReady for Phase 2: Data Source Integration")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
