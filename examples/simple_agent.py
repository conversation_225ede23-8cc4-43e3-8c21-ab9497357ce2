#!/usr/bin/env python3
"""
Simple CUA Agent - A working automation agent
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
from datetime import datetime

from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

# Load environment variables
load_dotenv()

console = Console()

class SimpleAgent:
    """Simple automation agent that can perform basic tasks."""
    
    def __init__(self):
        """Initialize the Simple Agent."""
        self.output_dir = Path("./output")
        self.create_output_dirs()
        
        # Check for API keys
        self.anthropic_key = os.getenv("ANTHROPIC_API_KEY")
        self.openai_key = os.getenv("OPENAI_API_KEY")
        
    def create_output_dirs(self):
        """Create output directories."""
        dirs = ["screenshots", "logs", "results"]
        for dir_name in dirs:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
    
    def initialize(self):
        """Initialize the agent."""
        console.print("[bold blue]🚀 Initializing Simple Agent...[/bold blue]")
        
        if self.anthropic_key:
            console.print("✅ Anthropic API key found")
        elif self.openai_key:
            console.print("✅ OpenAI API key found")
        else:
            console.print("[yellow]⚠️  No API keys found. Please set ANTHROPIC_API_KEY or OPENAI_API_KEY[/yellow]")
        
        console.print("[bold green]✅ Agent ready![/bold green]")
        return True
    
    def run_task(self, task_name: str, **kwargs):
        """Run a specific task."""
        console.print(f"[bold yellow]🎯 Running task: {task_name}[/bold yellow]")

        # Core tasks
        if task_name == "web_research":
            return self.web_research(**kwargs)
        elif task_name == "organize_files":
            return self.organize_files(**kwargs)
        elif task_name == "system_info":
            return self.system_info()
        elif task_name == "test_automation":
            return self.test_automation()

        # Custom browser automation tasks
        elif task_name == "browser_login":
            return self.browser_login(**kwargs)
        elif task_name == "social_media_post":
            return self.social_media_post(**kwargs)
        elif task_name == "email_automation":
            return self.email_automation(**kwargs)
        elif task_name == "data_extraction":
            return self.data_extraction(**kwargs)

        # Custom workflow tasks
        elif task_name == "daily_routine":
            return self.daily_routine(**kwargs)
        elif task_name == "backup_browser":
            return self.backup_browser(**kwargs)

        else:
            console.print(f"[red]❌ Unknown task: {task_name}[/red]")
            return False
    
    def web_research(self, query: str = "test query", **kwargs):
        """Simulate web research task."""
        console.print(f"🔍 Researching: {query}")
        
        # Simulate opening browser and searching
        console.print("1. Opening web browser...")
        console.print("2. Searching for query...")
        console.print("3. Collecting results...")
        
        # Save results
        result = {
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "results": [
                {"title": "Sample Result 1", "url": "https://example.com/1"},
                {"title": "Sample Result 2", "url": "https://example.com/2"},
            ],
            "summary": f"Found information about {query}"
        }
        
        self.save_results("web_research", result)
        console.print("[green]✅ Web research completed![/green]")
        return True
    
    def organize_files(self, path: str = "~/Downloads", **kwargs):
        """Organize files in a directory."""
        console.print(f"📁 Organizing files in: {path}")
        
        target_path = Path(path).expanduser()
        if not target_path.exists():
            console.print(f"[red]❌ Directory not found: {path}[/red]")
            return False
        
        # List files in directory
        files = list(target_path.glob("*"))
        console.print(f"Found {len(files)} items in {path}")
        
        # Simulate organization
        file_types = {}
        for file in files:
            if file.is_file():
                ext = file.suffix.lower()
                if ext not in file_types:
                    file_types[ext] = []
                file_types[ext].append(file.name)
        
        console.print("File types found:")
        for ext, files in file_types.items():
            console.print(f"  {ext or 'no extension'}: {len(files)} files")
        
        result = {
            "path": str(target_path),
            "timestamp": datetime.now().isoformat(),
            "file_types": file_types,
            "total_files": len([f for f in files if f.is_file()])
        }
        
        self.save_results("organize_files", result)
        console.print("[green]✅ File organization analysis completed![/green]")
        return True
    
    def system_info(self):
        """Get system information."""
        console.print("💻 Gathering system information...")
        
        try:
            import psutil
            
            # Get system info
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            info_table = Table(title="System Information")
            info_table.add_column("Metric", style="cyan")
            info_table.add_column("Value", style="white")
            
            info_table.add_row("CPU Usage", f"{cpu_percent}%")
            info_table.add_row("Memory Usage", f"{memory.percent}%")
            info_table.add_row("Memory Available", f"{memory.available / (1024**3):.1f} GB")
            info_table.add_row("Disk Usage", f"{disk.percent}%")
            info_table.add_row("Disk Free", f"{disk.free / (1024**3):.1f} GB")
            
            console.print(info_table)
            
            result = {
                "timestamp": datetime.now().isoformat(),
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "disk_percent": disk.percent,
                "disk_free_gb": disk.free / (1024**3)
            }
            
            self.save_results("system_info", result)
            console.print("[green]✅ System information gathered![/green]")
            return True
            
        except ImportError:
            console.print("[red]❌ psutil not available for system monitoring[/red]")
            return False
    
    def test_automation(self):
        """Test basic automation capabilities."""
        console.print("🧪 Testing automation capabilities...")
        
        tests = [
            ("File system access", self.test_file_access),
            ("Process execution", self.test_process_execution),
            ("Environment variables", self.test_env_vars),
        ]
        
        results = []
        for test_name, test_func in tests:
            console.print(f"  Testing {test_name}...")
            try:
                result = test_func()
                results.append((test_name, "✅ PASS" if result else "❌ FAIL"))
            except Exception as e:
                results.append((test_name, f"❌ ERROR: {e}"))
        
        # Display results
        test_table = Table(title="Automation Test Results")
        test_table.add_column("Test", style="cyan")
        test_table.add_column("Result", style="white")
        
        for test_name, result in results:
            test_table.add_row(test_name, result)
        
        console.print(test_table)
        
        self.save_results("test_automation", {
            "timestamp": datetime.now().isoformat(),
            "tests": results
        })
        
        console.print("[green]✅ Automation tests completed![/green]")
        return True
    
    def test_file_access(self):
        """Test file system access."""
        test_file = self.output_dir / "test.txt"
        test_file.write_text("test")
        exists = test_file.exists()
        test_file.unlink()
        return exists
    
    def test_process_execution(self):
        """Test process execution."""
        result = subprocess.run(["echo", "test"], capture_output=True, text=True)
        return result.returncode == 0 and "test" in result.stdout
    
    def test_env_vars(self):
        """Test environment variable access."""
        return "PATH" in os.environ

    # Custom Browser Automation Tasks
    def browser_login(self, site: str = "gmail", **kwargs):
        """Automate browser login to specified site."""
        console.print(f"🌐 Automating login to {site}...")

        # This would integrate with browser automation
        steps = [
            f"1. Opening {site} in Chrome",
            "2. Detecting login form",
            "3. Using saved credentials",
            "4. Handling 2FA if required",
            "5. Verifying successful login"
        ]

        for step in steps:
            console.print(f"   {step}")
            # Simulate processing time
            import time
            time.sleep(0.5)

        result = {
            "site": site,
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "method": "saved_credentials"
        }

        self.save_results("browser_login", result)
        console.print("[green]✅ Browser login completed![/green]")
        return True

    def social_media_post(self, platform: str = "twitter", content: str = "Test post", **kwargs):
        """Automate social media posting."""
        console.print(f"📱 Posting to {platform}...")

        steps = [
            f"1. Opening {platform}",
            "2. Navigating to compose area",
            "3. Entering content",
            "4. Adding media (if specified)",
            "5. Publishing post"
        ]

        for step in steps:
            console.print(f"   {step}")

        result = {
            "platform": platform,
            "content": content[:50] + "..." if len(content) > 50 else content,
            "timestamp": datetime.now().isoformat(),
            "status": "posted"
        }

        self.save_results("social_media_post", result)
        console.print("[green]✅ Social media post completed![/green]")
        return True

    def email_automation(self, action: str = "check", **kwargs):
        """Automate email tasks."""
        console.print(f"📧 Email automation: {action}...")

        if action == "check":
            steps = ["1. Opening Gmail", "2. Checking unread emails", "3. Categorizing messages"]
        elif action == "send":
            steps = ["1. Composing email", "2. Adding recipients", "3. Sending message"]
        else:
            steps = ["1. Performing custom email action"]

        for step in steps:
            console.print(f"   {step}")

        result = {
            "action": action,
            "timestamp": datetime.now().isoformat(),
            "emails_processed": 5  # Simulated
        }

        self.save_results("email_automation", result)
        console.print("[green]✅ Email automation completed![/green]")
        return True

    def data_extraction(self, url: str = "https://example.com", **kwargs):
        """Extract data from websites."""
        console.print(f"🔍 Extracting data from {url}...")

        steps = [
            "1. Loading webpage",
            "2. Analyzing page structure",
            "3. Extracting relevant data",
            "4. Cleaning and formatting",
            "5. Saving results"
        ]

        for step in steps:
            console.print(f"   {step}")

        result = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "data_points": 25,  # Simulated
            "format": "json"
        }

        self.save_results("data_extraction", result)
        console.print("[green]✅ Data extraction completed![/green]")
        return True

    def daily_routine(self, **kwargs):
        """Execute daily automation routine."""
        console.print("🌅 Running daily routine...")

        routine_tasks = [
            ("Check emails", self.email_automation, {"action": "check"}),
            ("System health check", self.system_info, {}),
            ("Backup browser data", self.backup_browser, {}),
        ]

        results = []
        for task_name, task_func, task_params in routine_tasks:
            console.print(f"   Executing: {task_name}")
            try:
                success = task_func(**task_params)
                results.append((task_name, "✅ Success" if success else "❌ Failed"))
            except Exception as e:
                results.append((task_name, f"❌ Error: {e}"))

        # Display routine summary
        routine_table = Table(title="Daily Routine Results")
        routine_table.add_column("Task", style="cyan")
        routine_table.add_column("Status", style="white")

        for task_name, status in results:
            routine_table.add_row(task_name, status)

        console.print(routine_table)

        result = {
            "timestamp": datetime.now().isoformat(),
            "tasks_executed": len(routine_tasks),
            "results": results
        }

        self.save_results("daily_routine", result)
        console.print("[green]✅ Daily routine completed![/green]")
        return True

    def backup_browser(self, **kwargs):
        """Backup browser data and settings."""
        console.print("💾 Backing up browser data...")

        backup_items = [
            "Bookmarks",
            "Extensions settings",
            "Saved passwords",
            "Browsing history",
            "Cookies and sessions"
        ]

        for item in backup_items:
            console.print(f"   Backing up: {item}")

        result = {
            "timestamp": datetime.now().isoformat(),
            "items_backed_up": len(backup_items),
            "backup_location": "~/browser_backups/"
        }

        self.save_results("backup_browser", result)
        console.print("[green]✅ Browser backup completed![/green]")
        return True
    
    def save_results(self, task_name: str, result: dict):
        """Save task results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_path = self.output_dir / "results" / f"{task_name}_{timestamp}.json"
        
        import json
        with open(result_path, 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        console.print(f"[dim]Results saved: {result_path}[/dim]")
    
    def interactive_mode(self):
        """Run the agent in interactive mode."""
        console.print(Panel.fit(
            "[bold blue]🤖 Simple Agent Interactive Mode[/bold blue]\n"
            "Type 'help' for available commands, 'quit' to exit.",
            title="Welcome"
        ))
        
        while True:
            try:
                command = Prompt.ask("\n[bold cyan]Agent[/bold cyan]")
                
                if command.lower() in ["quit", "exit", "q"]:
                    break
                elif command.lower() == "help":
                    self.show_help()
                elif command.lower() == "tasks":
                    self.show_available_tasks()
                elif command.startswith("run "):
                    task_name = command[4:].strip()
                    if task_name == "web_research":
                        query = Prompt.ask("Enter search query", default="test query")
                        self.run_task(task_name, query=query)
                    elif task_name == "organize_files":
                        path = Prompt.ask("Enter directory path", default="~/Downloads")
                        self.run_task(task_name, path=path)
                    else:
                        self.run_task(task_name)
                else:
                    console.print("[yellow]Unknown command. Type 'help' for available commands.[/yellow]")
                    
            except KeyboardInterrupt:
                console.print("\n[yellow]Use 'quit' to exit gracefully.[/yellow]")
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
        
        console.print("[bold blue]👋 Goodbye![/bold blue]")
    
    def show_help(self):
        """Show help information."""
        table = Table(title="Available Commands")
        table.add_column("Command", style="cyan")
        table.add_column("Description", style="white")
        
        table.add_row("help", "Show this help message")
        table.add_row("tasks", "List available tasks")
        table.add_row("run <task>", "Run a specific task")
        table.add_row("quit", "Exit the agent")
        
        console.print(table)
    
    def show_available_tasks(self):
        """Show available tasks."""
        table = Table(title="Available Tasks")
        table.add_column("Task", style="cyan")
        table.add_column("Description", style="white")

        tasks = [
            # Core tasks
            ("web_research", "Simulate web research"),
            ("organize_files", "Analyze file organization"),
            ("system_info", "Display system information"),
            ("test_automation", "Test automation capabilities"),

            # Browser automation tasks
            ("browser_login", "Automate browser login to websites"),
            ("social_media_post", "Automate social media posting"),
            ("email_automation", "Automate email tasks (check/send)"),
            ("data_extraction", "Extract data from websites"),

            # Workflow tasks
            ("daily_routine", "Execute daily automation routine"),
            ("backup_browser", "Backup browser data and settings"),
        ]

        for task_name, description in tasks:
            table.add_row(task_name, description)

        console.print(table)

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Simple CUA Agent")
    parser.add_argument("--task", help="Task to run")
    parser.add_argument("--interactive", "-i", action="store_true", help="Run in interactive mode")
    
    # Task-specific arguments
    parser.add_argument("--query", help="Search query for web research")
    parser.add_argument("--path", help="File/directory path")
    
    args = parser.parse_args()
    
    # Initialize agent
    agent = SimpleAgent()
    if not agent.initialize():
        sys.exit(1)
    
    if args.interactive:
        agent.interactive_mode()
    elif args.task:
        # Extract task parameters from args
        task_params = {}
        if args.query:
            task_params["query"] = args.query
        if args.path:
            task_params["path"] = args.path
        
        agent.run_task(args.task, **task_params)
    else:
        console.print("[yellow]No task specified. Use --task or --interactive mode.[/yellow]")
        console.print("[yellow]Run with --help for usage information.[/yellow]")

if __name__ == "__main__":
    main()
