#!/bin/bash
# LUME VM Startup Script for Job Collection
# Generated from template by setup_persistent_vm.py

echo "🚀 Starting persistent CUA job collection VM..."

# Get the project root directory (where this script is called from)
PROJECT_ROOT="$(pwd)"

# Start VM with shared directories
lume run macos-sequoia-cua:latest \
    --shared-dir "${PROJECT_ROOT}/lume_data/browser_profiles:/Users/<USER>/browser_profiles:rw" \
    --shared-dir "${PROJECT_ROOT}/lume_data/job_data:/Users/<USER>/job_data:rw" \
    --shared-dir "${PROJECT_ROOT}/lume_data/automation_scripts:/Users/<USER>/automation_scripts:rw" \
    --shared-dir "${PROJECT_ROOT}/lume_data/downloads:/Users/<USER>/Downloads:rw" \
    --storage persistent \
    --no-display

echo "✓ VM started with persistent storage"
echo "✓ Shared directories mounted"
echo "✓ Browser profiles will persist"
echo "✓ Job data will persist"
echo "✓ Ready for automation"
