#!/usr/bin/env python3
"""
LUME VM Persistence Setup

Sets up persistent storage and configuration for our CUA job collection VM.
Ensures browser state, data, and automation scripts persist between sessions.
"""

import os
import subprocess
import json
import shutil
from pathlib import Path
from pathlib import Path


def setup_persistent_vm():
    """Set up persistent VM with shared directories and storage."""
    print("🔧 Setting up persistent LUME VM for job collection...")
    
    # Create persistent directories
    persistent_dirs = [
        "lume_data",
        "lume_data/browser_profiles",
        "lume_data/job_data", 
        "lume_data/automation_scripts",
        "lume_data/downloads",
        "lume_data/logs"
    ]
    
    for directory in persistent_dirs:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created persistent directory: {directory}")
    
    # Stop current VM if running
    print("\n🛑 Stopping current VM...")
    try:
        subprocess.run(["lume", "stop", "macos-sequoia-cua_latest"], 
                      capture_output=True, check=False)
        print("✓ VM stopped")
    except:
        print("✓ No VM to stop")
    
    # Create VM configuration
    vm_config = {
        "name": "cua-job-collector",
        "base_image": "macos-sequoia-cua:latest",
        "shared_directories": [
            f"{os.getcwd()}/lume_data/browser_profiles:/Users/<USER>/browser_profiles:rw",
            f"{os.getcwd()}/lume_data/job_data:/Users/<USER>/job_data:rw",
            f"{os.getcwd()}/lume_data/automation_scripts:/Users/<USER>/automation_scripts:rw",
            f"{os.getcwd()}/lume_data/downloads:/Users/<USER>/Downloads:rw"
        ],
        "storage": "persistent"
    }
    
    # Save configuration
    with open("lume_data/vm_config.json", "w") as f:
        json.dump(vm_config, f, indent=2)
    print("✓ VM configuration saved")
    
    # Create startup script from template
    template_path = Path(__file__).parent / "templates" / "start_persistent_vm.sh.template"
    dest_script = Path("scripts/setup/start_persistent_vm.sh")
    dest_script.parent.mkdir(parents=True, exist_ok=True)

    if template_path.exists():
        # Copy template to destination
        shutil.copy2(template_path, dest_script)
        # Make script executable
        os.chmod(dest_script, 0o755)
        print("✓ Startup script created from template")
    else:
        print("⚠️ Startup script template not found, creating basic version")
        # Fallback to simple version if template missing
        basic_script = """#!/bin/bash
echo "🚀 Starting CUA VM..."
lume run macos-sequoia-cua:latest --storage persistent
"""
        with open(dest_script, "w") as f:
            f.write(basic_script)
        os.chmod(dest_script, 0o755)
    
    # Copy the existing browser profile setup script (no need to generate!)
    source_script = Path(__file__).parent / "setup_browser_profiles.py"
    dest_script = Path("scripts/setup/setup_browser_profiles.py")
    dest_script.parent.mkdir(parents=True, exist_ok=True)

    if source_script.exists():
        shutil.copy2(source_script, dest_script)
        os.chmod(dest_script, 0o755)  # Make executable
        print("✓ Browser setup script copied")
    else:
        print("⚠️ Source browser setup script not found, skipping")
    
    print("\n🎉 Persistent VM setup completed!")
    print("\nNext steps:")
    print("1. Run: ./scripts/setup/start_persistent_vm.sh")
    print("2. Run: python scripts/setup/setup_browser_profiles.py")
    print("3. Manually configure browser profiles")
    print("4. Start job collection automation")
    
    print("\n📁 Persistent directories:")
    for directory in persistent_dirs:
        print(f"   {directory}/")
    
    return True


if __name__ == "__main__":
    setup_persistent_vm()
