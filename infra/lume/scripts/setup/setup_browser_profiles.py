#!/usr/bin/env python3
'''
Browser Profile Setup for Persistent VM

Sets up Chrome/Safari profiles with saved logins, extensions, and bookmarks
for job collection automation.
'''

import asyncio
from computer import Computer


async def setup_browser_profiles():
    '''Set up persistent browser profiles in the VM.'''
    print("🌐 Setting up browser profiles...")
    
    computer = Computer(os_type="macos")
    await computer.run()
    
    # Create Chrome profile directory
    await computer.interface.type_text("mkdir -p ~/browser_profiles/chrome")
    await computer.interface.press_key("return")
    
    # Create Safari bookmarks backup location
    await computer.interface.type_text("mkdir -p ~/browser_profiles/safari")
    await computer.interface.press_key("return")
    
    print("✓ Browser profile directories created")
    print("✓ Profiles will persist between VM sessions")
    print("✓ Login states will be preserved")
    
    # Instructions for manual setup
    print("\n📋 Manual Setup Instructions:")
    print("1. Open Chrome and sign in to job sites")
    print("2. Install useful extensions (uBlock, etc.)")
    print("3. Save bookmarks for job sites")
    print("4. Profiles will automatically persist")


if __name__ == "__main__":
    asyncio.run(setup_browser_profiles())
