#!/usr/bin/env python3
"""
Video Recording Test

Simple test to verify video recording works.
Records 5 seconds of screen activity.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.job_collector.utils.session_recorder import SessionRecorder


async def test_video_recording():
    """Test video recording for 5 seconds."""
    print("🎥 Video Recording Test")
    print("=" * 30)
    print("Recording 5 seconds of screen activity...")
    print("Move your mouse or do something visible!")
    print()
    
    with SessionRecorder("video_test", record_video=True) as recorder:
        recorder.add_metadata("test_type", "video_recording")
        
        print("📹 Recording started...")
        await asyncio.sleep(5)
        print("📹 Recording stopping...")
    
    print("\n✅ Video recording test completed!")
    print("📁 Check output/sessions/video_test/ for the video file")


if __name__ == "__main__":
    asyncio.run(test_video_recording())
