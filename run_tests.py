#!/usr/bin/env python3
"""
CUA Agent Test Runner

Simple test runner for the main functionality.
All outputs go to output/ directory.
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run tests with a simple menu."""
    print("🧪 CUA Agent Test Runner")
    print("=" * 40)
    print("Available tests:")
    print("1. Basic Agent Test (recommended)")
    print("2. Video Recording Test")
    print("3. Advanced Tests (in tests/ directory)")
    print("0. Exit")
    print()

    while True:
        choice = input("Select test (0-3): ").strip()

        if choice == "0":
            print("👋 Goodbye!")
            break
        elif choice == "1":
            print("\n🚀 Running Basic Agent Test...")
            subprocess.run([sys.executable, "test_basic.py"])
        elif choice == "2":
            print("\n🎥 Running Video Recording Test...")
            subprocess.run([sys.executable, "test_video.py"])
        elif choice == "3":
            print("\n🔧 Running Advanced Tests...")
            subprocess.run([sys.executable, "tests/run_tests.py"])
        else:
            print("❌ Invalid choice. Please select 0-3.")

        print("\n" + "=" * 40)

if __name__ == "__main__":
    main()
