#!/usr/bin/env python3
"""
Test script for CUA Agent
"""

import asyncio
import sys
from pathlib import Path

# Add CUA path
cua_path = Path.home() / "util" / "cua"
if cua_path.exists():
    sys.path.insert(0, str(cua_path))

from rich.console import Console
from rich.panel import Panel

console = Console()

async def test_imports():
    """Test if all required imports work."""
    console.print("[bold blue]Testing imports...[/bold blue]")
    
    try:
        # Test CUA imports
        from computer import Computer
        from agent import ComputerAgent, LLM, AgentLoop, LLMProvider
        console.print("✅ CUA framework imports successful")
        
        # Test other imports
        import yaml
        import json
        from dotenv import load_dotenv
        console.print("✅ All dependencies imported successfully")
        
        return True
    except ImportError as e:
        console.print(f"❌ Import error: {e}")
        return False

async def test_configuration():
    """Test configuration loading."""
    console.print("[bold blue]Testing configuration...[/bold blue]")
    
    try:
        # Test environment file
        env_file = Path(".env")
        if env_file.exists():
            console.print("✅ .env file found")
        else:
            console.print("⚠️  .env file not found (using defaults)")
        
        # Test config file
        config_file = Path("config/settings.yaml")
        if config_file.exists():
            import yaml
            with open(config_file) as f:
                config = yaml.safe_load(f)
            console.print("✅ Configuration file loaded successfully")
        else:
            console.print("⚠️  Configuration file not found (using defaults)")
        
        return True
    except Exception as e:
        console.print(f"❌ Configuration error: {e}")
        return False

async def test_agent_initialization():
    """Test agent initialization without actually starting VM."""
    console.print("[bold blue]Testing agent initialization...[/bold blue]")
    
    try:
        # Import main agent class
        from main import CUAAgent
        
        # Create agent instance
        agent = CUAAgent()
        console.print("✅ Agent instance created successfully")
        
        # Test configuration loading
        config = agent.config
        console.print(f"✅ Configuration loaded: {len(config)} sections")
        
        return True
    except Exception as e:
        console.print(f"❌ Agent initialization error: {e}")
        return False

async def test_output_directories():
    """Test output directory creation."""
    console.print("[bold blue]Testing output directories...[/bold blue]")
    
    try:
        output_dir = Path("output")
        subdirs = ["screenshots", "logs", "results"]
        
        for subdir in subdirs:
            dir_path = output_dir / subdir
            if dir_path.exists():
                console.print(f"✅ {subdir} directory exists")
            else:
                dir_path.mkdir(parents=True, exist_ok=True)
                console.print(f"✅ {subdir} directory created")
        
        return True
    except Exception as e:
        console.print(f"❌ Output directory error: {e}")
        return False

async def main():
    """Run all tests."""
    console.print(Panel.fit(
        "[bold green]CUA Agent Test Suite[/bold green]\n"
        "Testing agent setup and configuration...",
        title="🧪 Testing"
    ))
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Agent Initialization Test", test_agent_initialization),
        ("Output Directories Test", test_output_directories),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        console.print(f"\n[bold yellow]Running {test_name}...[/bold yellow]")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            console.print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    console.print("\n" + "=" * 50)
    console.print("[bold blue]Test Results Summary:[/bold blue]")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        console.print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    console.print(f"\nTests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        console.print("\n[bold green]🎉 All tests passed! Agent is ready to use.[/bold green]")
        console.print("\n📋 Next steps:")
        console.print("1. Make sure your API keys are set in .env file")
        console.print("2. Run: python main.py --interactive")
        console.print("3. Try a simple task: python main.py --task web_research --query 'test'")
    else:
        console.print("\n[bold red]❌ Some tests failed. Please check the setup.[/bold red]")
        console.print("\n🔧 Troubleshooting:")
        console.print("1. Run: python setup.py")
        console.print("2. Check that CUA framework is installed at ~/util/cua/")
        console.print("3. Ensure all dependencies are installed")

if __name__ == "__main__":
    asyncio.run(main())
