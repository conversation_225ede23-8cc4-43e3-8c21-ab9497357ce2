#!/usr/bin/env python3
"""
Test script for the Indeed adapter.

This script tests the Indeed adapter with browser automation to collect real job data.
"""

import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from job_collector.adapters.indeed_adapter import IndeedAdapter
from job_collector.storage.json_storage import JSONStorage
from job_collector.core.models import SearchParameters, JobType, ExperienceLevel
from job_collector.collectors.base_analyzer import BaseResultsAnalyzer


def test_indeed_search():
    """Test basic Indeed job search functionality."""
    print("🔍 Testing Indeed Adapter")
    print("=" * 50)
    
    # Create search parameters
    search_params = SearchParameters(
        keywords=["python", "software engineer"],
        location="San Francisco, CA",
        job_types=[JobType.FULL_TIME],
        experience_levels=[ExperienceLevel.MID_LEVEL, ExperienceLevel.SENIOR_LEVEL],
        max_results=10,  # Start small for testing
        posted_since_days=7
    )
    
    print(f"Search Parameters:")
    print(f"  Keywords: {search_params.keywords}")
    print(f"  Location: {search_params.location}")
    print(f"  Job Types: {[jt.value for jt in search_params.job_types]}")
    print(f"  Max Results: {search_params.max_results}")
    print(f"  Posted Since: {search_params.posted_since_days} days")
    print()
    
    # Initialize adapter
    print("🚀 Initializing Indeed adapter...")
    adapter = IndeedAdapter(headless=False)  # Set to False to see browser in action
    
    try:
        # Perform search
        print("🔍 Performing job search...")
        start_time = datetime.now()
        
        result = adapter.search_jobs(search_params)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Display results
        print(f"\n📊 Search Results:")
        print(f"  Platform: {result.platform}")
        print(f"  Success: {result.success}")
        print(f"  Total Collected: {result.total_collected}")
        print(f"  Duration: {duration:.2f} seconds")
        print(f"  Pages Processed: {result.pages_processed}")
        
        if result.errors:
            print(f"  Errors: {result.errors}")
        
        # Show sample jobs
        if result.job_listings:
            print(f"\n📋 Sample Job Listings:")
            for i, job in enumerate(result.job_listings[:3], 1):
                print(f"\n  Job {i}:")
                print(f"    Title: {job.title}")
                print(f"    Company: {job.company}")
                print(f"    Location: {job.location}")
                print(f"    Remote Type: {job.remote_type.value if job.remote_type else 'Unknown'}")
                if job.salary_min or job.salary_max:
                    salary_range = f"${job.salary_min:,}" if job.salary_min else "N/A"
                    if job.salary_max:
                        salary_range += f" - ${job.salary_max:,}"
                    print(f"    Salary: {salary_range}")
                print(f"    URL: {job.url}")
                if job.description:
                    desc_preview = job.description[:100] + "..." if len(job.description) > 100 else job.description
                    print(f"    Description: {desc_preview}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_storage_integration(result):
    """Test storage integration with collected results."""
    if not result or not result.job_listings:
        print("⚠️  No results to store")
        return
    
    print(f"\n💾 Testing Storage Integration")
    print("=" * 50)
    
    try:
        # Initialize storage
        storage = JSONStorage("./test_output/indeed_test")
        
        # Save collection result
        print("💾 Saving collection result...")
        storage.save_collection_result(result)
        print(f"✅ Saved {len(result.job_listings)} job listings")
        
        # Test retrieval
        print("🔍 Testing job retrieval...")
        first_job = result.job_listings[0]
        retrieved_job = storage.get_job_listing(first_job.id)
        
        if retrieved_job:
            print(f"✅ Successfully retrieved job: {retrieved_job.title}")
        else:
            print("❌ Failed to retrieve job")
        
        # Test search
        print("🔍 Testing job search...")
        search_results = storage.search_stored_jobs({"keywords": ["python"]})
        print(f"✅ Found {len(search_results)} jobs matching 'python'")
        
        # Export data
        print("📤 Testing data export...")
        export_path = storage.export_data("json")
        print(f"✅ Exported data to: {export_path}")
        
    except Exception as e:
        print(f"❌ Storage test failed: {e}")
        import traceback
        traceback.print_exc()


def test_analysis(result):
    """Test analysis capabilities on collected results."""
    if not result or not result.job_listings:
        print("⚠️  No results to analyze")
        return
    
    print(f"\n📊 Testing Analysis Capabilities")
    print("=" * 50)
    
    try:
        analyzer = BaseResultsAnalyzer()
        
        # Test filtering
        print("🔍 Testing job filtering...")
        python_jobs = analyzer.filter_results(result.job_listings, {"keywords": ["python"]})
        print(f"✅ Found {len(python_jobs)} Python jobs")
        
        remote_jobs = analyzer.filter_results(result.job_listings, {"remote_types": ["remote"]})
        print(f"✅ Found {len(remote_jobs)} remote jobs")
        
        # Test insights
        print("📈 Extracting insights...")
        insights = analyzer.extract_insights(result.job_listings)
        
        print(f"📊 Job Market Insights:")
        print(f"  Total Jobs: {insights.get('total_jobs', 0)}")
        print(f"  Unique Companies: {insights.get('unique_companies', 0)}")
        
        if 'remote_type_distribution' in insights:
            print(f"  Remote Work Distribution:")
            for remote_type, count in insights['remote_type_distribution'].items():
                print(f"    {remote_type}: {count}")
        
        if 'top_companies' in insights:
            print(f"  Top Companies:")
            for company, count in list(insights['top_companies'].items())[:3]:
                print(f"    {company}: {count} jobs")
        
        if 'salary_stats' in insights:
            salary_stats = insights['salary_stats']
            print(f"  Salary Statistics:")
            print(f"    Min: ${salary_stats['min']:,.0f}")
            print(f"    Max: ${salary_stats['max']:,.0f}")
            print(f"    Average: ${salary_stats['avg']:,.0f}")
        
    except Exception as e:
        print(f"❌ Analysis test failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run the complete Indeed adapter test."""
    print("🤖 Indeed Adapter Test Suite")
    print("=" * 60)
    print("This test will:")
    print("1. Search for Python jobs in San Francisco on Indeed")
    print("2. Extract job listings using browser automation")
    print("3. Store results in JSON format")
    print("4. Analyze the collected data")
    print()
    
    # Test 1: Job Search
    result = test_indeed_search()
    
    if result and result.success:
        # Test 2: Storage
        test_storage_integration(result)
        
        # Test 3: Analysis
        test_analysis(result)
        
        print(f"\n🎉 Test Complete!")
        print(f"✅ Successfully collected {result.total_collected} jobs from Indeed")
        print(f"✅ Data stored and analyzed successfully")
        
    else:
        print(f"\n❌ Test Failed!")
        print("Check the error messages above for details")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
