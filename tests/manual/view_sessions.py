#!/usr/bin/env python3
"""
View recorded computer use agent sessions.

Lists all recorded sessions and allows viewing detailed session data.
"""

import json
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.job_collector.utils.session_recorder import list_sessions


def main():
    """Main function to view sessions."""
    print("📹 Computer Use Agent Session Viewer")
    print("=" * 50)
    
    # List all sessions
    sessions = list_sessions()
    
    if not sessions:
        print("No recorded sessions found.")
        print("Run test_with_recording.py to create a session.")
        return
    
    print(f"Found {len(sessions)} recorded sessions:\n")
    
    # Display sessions
    for i, session in enumerate(sessions, 1):
        print(f"{i:2d}. {session['name']}")
        print(f"    Start: {session['start_time']}")
        print(f"    Status: {session['status']}")
        print(f"    Tasks: {session['tasks']}, Screenshots: {session['screenshots']}")
        print(f"    Path: {session['path']}")
        print()
    
    # Let user select a session to view
    try:
        choice = input("Enter session number to view details (or Enter to exit): ").strip()
        if not choice:
            return
        
        session_num = int(choice)
        if 1 <= session_num <= len(sessions):
            view_session_details(sessions[session_num - 1])
        else:
            print("Invalid session number.")
    
    except ValueError:
        print("Invalid input.")
    except KeyboardInterrupt:
        print("\nExiting.")


def view_session_details(session_info):
    """View detailed information about a session."""
    session_path = Path(session_info['path'])
    session_file = session_path / "session.json"
    report_file = session_path / "session_report.md"
    
    print(f"\n📋 Session Details: {session_info['name']}")
    print("=" * 60)
    
    # Load full session data
    try:
        with open(session_file) as f:
            session_data = json.load(f)
    except Exception as e:
        print(f"❌ Failed to load session data: {e}")
        return
    
    # Display session overview
    print(f"Start Time: {session_data['start_time']}")
    print(f"End Time: {session_data.get('end_time', 'In Progress')}")
    print(f"Status: {session_data['status']}")
    print(f"Tasks: {len(session_data['tasks'])}")
    print(f"Screenshots: {len(session_data['screenshots'])}")
    print(f"Agent Responses: {len(session_data['agent_responses'])}")
    print(f"Errors: {len(session_data['errors'])}")
    
    # Display metadata
    if session_data.get('metadata'):
        print(f"\nMetadata:")
        for key, value in session_data['metadata'].items():
            print(f"  {key}: {value}")
    
    # Display tasks
    if session_data['tasks']:
        print(f"\nTasks:")
        for task in session_data['tasks']:
            print(f"  {task['task_id']}. {task['description']}")
            print(f"     Type: {task['type']}, Status: {task['status']}")
            if task.get('summary'):
                print(f"     Summary: {task['summary']}")
            if task['screenshots']:
                print(f"     Screenshots: {len(task['screenshots'])}")
            if task['errors']:
                print(f"     Errors: {len(task['errors'])}")
    
    # Display recent agent responses
    if session_data['agent_responses']:
        print(f"\nRecent Agent Responses:")
        for response in session_data['agent_responses'][-5:]:  # Last 5 responses
            print(f"  [{response['timestamp']}] {response['type']}: {response['content'][:100]}...")
    
    # Display errors
    if session_data['errors']:
        print(f"\nErrors:")
        for error in session_data['errors']:
            print(f"  [{error['timestamp']}] {error['type']}: {error['message']}")
    
    print(f"\n📁 Session files:")
    print(f"  JSON data: {session_file}")
    if report_file.exists():
        print(f"  Report: {report_file}")
    
    # List screenshot files
    screenshots = list(session_path.glob("*.png"))
    if screenshots:
        print(f"  Screenshots ({len(screenshots)}):")
        for screenshot in sorted(screenshots):
            print(f"    {screenshot.name}")
    
    print(f"\n💡 To view screenshots, open the files in: {session_path}")


if __name__ == "__main__":
    main()
