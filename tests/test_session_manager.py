"""Test CUASessionManager functionality.

This test suite verifies that the CUASessionManager properly manages
VM lifecycle and provides reliable access to Computer and Agent instances.
"""

import pytest
import asyncio
from src.cua_session_manager import CUASessionManager, run_cua_task


class TestCUASessionManager:
    """Test suite for CUASessionManager."""
    
    @pytest.mark.asyncio
    async def test_session_manager_lifecycle(self):
        """Test that session manager properly starts and stops VM."""
        
        async with CUASessionManager() as session:
            # Verify session is active
            assert session.is_active
            
            # Test computer access
            computer = session.get_computer()
            assert computer is not None
            
            # Test screenshot (proves VM is running)
            screenshot = await computer.interface.screenshot()
            assert len(screenshot) > 1000  # Should be substantial
            
            # Test agent creation
            agent = session.get_agent()
            assert agent is not None
            
            # Test convenience screenshot method
            screenshot2 = await session.take_screenshot()
            assert len(screenshot2) > 1000
        
        # Verify session is no longer active after exit
        assert not session.is_active

    def test_session_manager_error_outside_context(self):
        """Test that accessing session outside context raises error."""
        session = CUASessionManager()
        
        # Should not be active before entering context
        assert not session.is_active
        
        with pytest.raises(RuntimeError, match="Session not active"):
            session.get_computer()
        
        with pytest.raises(RuntimeError, match="Session not active"):
            session.get_agent()

    @pytest.mark.asyncio
    async def test_session_manager_with_different_providers(self):
        """Test session manager with different AI providers."""
        
        # Test OpenAI provider
        async with CUASessionManager(provider="openai", model="gpt-4o") as session:
            agent = session.get_agent()
            assert agent is not None
        
        # Test Anthropic provider
        async with CUASessionManager(provider="anthropic", model="claude-3-5-sonnet-20241022") as session:
            agent = session.get_agent()
            assert agent is not None

    def test_session_manager_invalid_provider(self):
        """Test that invalid provider raises error."""
        session = CUASessionManager(provider="invalid_provider")
        
        async def test_invalid():
            async with session:
                with pytest.raises(ValueError, match="Unsupported provider"):
                    session.get_agent()
        
        asyncio.run(test_invalid())

    @pytest.mark.asyncio
    async def test_agent_reuse(self):
        """Test that agent is reused within the same session."""
        
        async with CUASessionManager() as session:
            agent1 = session.get_agent()
            agent2 = session.get_agent()
            
            # Should be the same instance
            assert agent1 is agent2

    @pytest.mark.asyncio
    async def test_convenience_task_runner(self):
        """Test the convenience run_agent_task method."""
        
        async with CUASessionManager() as session:
            response = await session.run_agent_task("Take a screenshot and describe what you see")
            assert isinstance(response, str)
            assert len(response) > 0

    @pytest.mark.asyncio
    async def test_standalone_task_function(self):
        """Test the standalone run_cua_task function."""
        
        response = await run_cua_task("Take a screenshot and describe what you see")
        assert isinstance(response, str)
        assert len(response) > 0


class TestCUASessionManagerIntegration:
    """Integration tests that require actual VM interaction."""
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_full_agent_interaction(self):
        """Test full agent interaction with actual task execution."""
        
        async with CUASessionManager() as session:
            agent = session.get_agent()
            
            # Run a simple task
            task = "Take a screenshot and tell me what you see on the screen"
            
            responses = []
            async for response in agent.run(task):
                responses.append(response)
                break  # Just get the first response
            
            assert len(responses) > 0
            assert isinstance(responses[0], dict) or isinstance(responses[0], str)

    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_multiple_tasks_same_session(self):
        """Test running multiple tasks in the same session."""
        
        async with CUASessionManager() as session:
            # First task
            response1 = await session.run_agent_task("Take a screenshot")
            assert len(response1) > 0
            
            # Second task in same session
            response2 = await session.run_agent_task("Describe what you see")
            assert len(response2) > 0

    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_session_recovery_after_error(self):
        """Test that session can handle errors gracefully."""
        
        try:
            async with CUASessionManager() as session:
                # This should work
                computer = session.get_computer()
                assert computer is not None
                
                # Simulate an error
                raise ValueError("Test error")
                
        except ValueError:
            # Error should be handled gracefully
            pass
        
        # Should be able to create a new session after error
        async with CUASessionManager() as session:
            computer = session.get_computer()
            assert computer is not None


# Pytest configuration
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])
