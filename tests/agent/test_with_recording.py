#!/usr/bin/env python3
"""
Computer use agent test with full session recording.

This script tests the agent's computer use capabilities while recording:
- All agent outputs and responses
- Video recording of the entire session
- Task progression and results
- Any errors that occur

Creates a complete audit trail of the test session with video proof.
"""

import os
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError:
    CUA_AVAILABLE = False
    print("❌ CUA framework not available. Install with: pip install cua-agent[omni]")
    exit(1)

from src.job_collector.utils.session_recorder import SessionRecorder


class RecordedAgentTester:
    """Test computer use agent with full session recording."""
    
    def __init__(self, model_provider: str = "openai", model_name: str = "gpt-4o"):
        self.model_provider = model_provider.upper()
        self.model_name = model_name
        self.agent = None
        self.computer = None
        self.recorder = None
        
        # Validate provider
        self._validate_provider()
    
    def _validate_provider(self):
        """Validate that the specified provider is available."""
        provider_map = {
            "OPENAI": ("OPENAI_API_KEY", LLMProvider.OPENAI),
            "ANTHROPIC": ("ANTHROPIC_API_KEY", LLMProvider.ANTHROPIC),
            "OLLAMA": (None, LLMProvider.OLLAMA),
        }
        
        if self.model_provider not in provider_map:
            raise ValueError(f"Unsupported provider: {self.model_provider}")
        
        api_key_env, self.llm_provider = provider_map[self.model_provider]
        
        if api_key_env and not os.getenv(api_key_env):
            raise ValueError(f"Missing {api_key_env} environment variable")
    
    async def run_recorded_test(self, test_name: str = "computer_use_test"):
        """Run a computer use test with full recording."""
        
        # Start session recording
        with SessionRecorder(session_name=test_name) as recorder:
            self.recorder = recorder
            
            # Add test metadata
            recorder.add_metadata("test_type", "computer_use_agent")
            recorder.add_metadata("model_provider", self.model_provider)
            recorder.add_metadata("model_name", self.model_name)
            recorder.add_metadata("framework", "CUA_OMNI")
            
            try:
                async with Computer() as computer:
                    self.computer = computer
                    
                    # Create agent
                    self.agent = ComputerAgent(
                        computer=computer,
                        loop=AgentLoop.OMNI,
                        model=LLM(provider=self.llm_provider, name=self.model_name)
                    )
                    
                    recorder.record_agent_response(
                        f"Agent initialized with {self.model_provider}:{self.model_name}",
                        response_type="initialization"
                    )
                    
                    print(f"✅ Agent initialized - recording session: {test_name}")
                    
                    # Test 1: Basic navigation
                    await self._test_navigation(recorder)
                    
                    # Test 2: Element detection
                    await self._test_element_detection(recorder)
                    
                    # Test 3: Simple interaction
                    await self._test_interaction(recorder)
                    
                    print("🏁 All tests completed")
                    
            except Exception as e:
                recorder.record_error(f"Test failed: {str(e)}", error_type="test_failure")
                raise
    
    async def _test_navigation(self, recorder: SessionRecorder):
        """Test basic navigation with recording."""
        task_id = recorder.start_task("Navigate to example.com", "navigation")

        try:
            # Navigate to example.com
            task = "Open a web browser and navigate to example.com"
            recorder.record_agent_response(f"Starting task: {task}", task_id, "task_start")

            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    recorder.record_agent_response(text, task_id, "agent_output")
                    print(f"   Agent: {text}")

            # Wait for page to load
            await asyncio.sleep(5)

            recorder.end_task(task_id, "completed", f"Successfully navigated to example.com")

        except Exception as e:
            recorder.record_error(f"Navigation failed: {str(e)}", task_id, "navigation_error")
            recorder.end_task(task_id, "failed", f"Navigation failed: {str(e)}")
            raise
    
    async def _test_element_detection(self, recorder: SessionRecorder):
        """Test element detection with recording."""
        task_id = recorder.start_task("Test element detection on current page", "detection")

        try:
            # Test element detection
            task = "Look at the current page and describe what interactive elements you can see"
            recorder.record_agent_response(f"Starting detection task: {task}", task_id, "task_start")

            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    recorder.record_agent_response(text, task_id, "agent_output")
                    print(f"   Agent: {text}")

            # Check if any elements were detected by looking at recent trajectories
            detection_count = await self._count_detected_elements()
            recorder.add_metadata(f"task_{task_id}_elements_detected", detection_count)

            if detection_count > 0:
                recorder.end_task(task_id, "completed", f"Detected {detection_count} elements")
            else:
                recorder.end_task(task_id, "completed", "No elements detected (possible threshold issue)")

        except Exception as e:
            recorder.record_error(f"Detection test failed: {str(e)}", task_id, "detection_error")
            recorder.end_task(task_id, "failed", f"Detection test failed: {str(e)}")
            raise
    
    async def _test_interaction(self, recorder: SessionRecorder):
        """Test simple interaction with recording."""
        task_id = recorder.start_task("Test clicking on 'More information...' link", "interaction")

        try:
            # Attempt interaction
            task = "Click on the 'More information...' link on the page"
            recorder.record_agent_response(f"Starting interaction task: {task}", task_id, "task_start")

            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    recorder.record_agent_response(text, task_id, "agent_output")
                    print(f"   Agent: {text}")

            # Wait for any page changes
            await asyncio.sleep(5)

            recorder.end_task(task_id, "completed", "Interaction attempt completed")

        except Exception as e:
            recorder.record_error(f"Interaction test failed: {str(e)}", task_id, "interaction_error")
            recorder.end_task(task_id, "failed", f"Interaction test failed: {str(e)}")
            raise
    

    
    async def _count_detected_elements(self) -> int:
        """Count elements detected in the most recent trajectory."""
        try:
            trajectories_dir = Path("trajectories")
            if not trajectories_dir.exists():
                return 0
            
            runs = sorted(trajectories_dir.glob("*"), key=lambda x: x.name, reverse=True)
            if not runs:
                return 0
            
            latest_run = runs[0]
            turn_dirs = sorted([d for d in latest_run.iterdir() if d.is_dir() and d.name.startswith('turn_')], 
                             key=lambda x: x.name, reverse=True)
            
            for turn_dir in turn_dirs:
                elements_file = turn_dir / "elements.json"
                if elements_file.exists():
                    with open(elements_file) as f:
                        elements_data = f.read().strip()
                        if elements_data == "[]":
                            return 0
                        else:
                            # Count elements (rough estimate)
                            return elements_data.count('"id"')
            
            return 0
            
        except Exception:
            return 0


async def main():
    """Main test function."""
    print("🚀 Computer Use Agent Test with Session Recording")
    print("This will test actual computer use capabilities and record everything")
    print()
    
    # Get test name from user
    test_name = input("Enter test name (or press Enter for default): ").strip()
    if not test_name:
        test_name = f"computer_use_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        tester = RecordedAgentTester()
        await tester.run_recorded_test(test_name)
        
        print(f"\n🎉 Test completed! Session recorded as: {test_name}")
        print("📁 Check the session_recordings directory for complete logs and video recording")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
