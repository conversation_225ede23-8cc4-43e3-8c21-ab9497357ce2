#!/usr/bin/env python3
"""
Simple computer use agent test with recording.
"""

import os
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError:
    CUA_AVAILABLE = False
    print("❌ CUA framework not available")
    exit(1)

from src.job_collector.utils.session_recorder import SessionRecorder


async def run_simple_test():
    """Run a simple computer use test with recording."""
    
    test_name = f"simple_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Start session recording with video
    with SessionRecorder(session_name=test_name, record_video=True) as recorder:
        
        # Add test metadata
        recorder.add_metadata("test_type", "simple_computer_use")
        recorder.add_metadata("model_provider", "OPENAI")
        recorder.add_metadata("model_name", "gpt-4o")
        
        print(f"📹 Recording session: {test_name}")
        
        try:
            async with Computer() as computer:
                print("✅ Computer initialized")
                
                # Create agent
                agent = ComputerAgent(
                    computer=computer,
                    loop=AgentLoop.OMNI,
                    model=LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
                )
                
                print("✅ Agent initialized")
                recorder.record_agent_response("Agent initialized successfully", response_type="initialization")
                
                # Test 1: Simple navigation
                task_id = recorder.start_task("Navigate to example.com", "navigation")
                
                task = "Open a web browser and navigate to example.com"
                print(f"🤖 Task: {task}")
                recorder.record_agent_response(f"Starting task: {task}", task_id, "task_start")
                
                response_text = ""
                async for response in agent.run(task):
                    # Handle different response types safely
                    text_content = ""
                    if isinstance(response, dict):
                        # Safely get the text content, ensuring it's always a string
                        text_value = response.get("text")
                        if isinstance(text_value, str):
                            text_content = text_value
                        elif text_value is not None:
                            text_content = str(text_value)
                        else:
                            # If 'text' key is missing or None, represent the whole dict as a string for logging
                            text_content = str(response)
                    else:
                        text_content = str(response)

                    response_text += text_content
                    if text_content.strip():
                        recorder.record_agent_response(text_content, task_id, "agent_output")
                        print(f"   Agent: {text_content}")
                
                recorder.end_task(task_id, "completed", "Navigation task completed")
                
                # Wait a bit
                await asyncio.sleep(3)
                
                # Test 2: Simple description
                task_id = recorder.start_task("Describe the page", "description")
                
                task = "Describe what you see on the current page"
                print(f"🤖 Task: {task}")
                recorder.record_agent_response(f"Starting task: {task}", task_id, "task_start")
                
                response_text = ""
                async for response in agent.run(task):
                    # Handle different response types safely
                    text_content = ""
                    if isinstance(response, dict):
                        # Safely get the text content, ensuring it's always a string
                        text_value = response.get("text")
                        if isinstance(text_value, str):
                            text_content = text_value
                        elif text_value is not None:
                            text_content = str(text_value)
                        else:
                            # If 'text' key is missing or None, represent the whole dict as a string for logging
                            text_content = str(response)
                    else:
                        text_content = str(response)

                    response_text += text_content
                    if text_content.strip():
                        recorder.record_agent_response(text_content, task_id, "agent_output")
                        print(f"   Agent: {text_content}")
                
                recorder.end_task(task_id, "completed", "Description task completed")
                
                print("🏁 Test completed successfully")
                
        except Exception as e:
            recorder.record_error(f"Test failed: {str(e)}", error_type="test_failure")
            print(f"❌ Test failed: {e}")
            raise


if __name__ == "__main__":
    print("🚀 Simple Computer Use Agent Test with Recording")
    print("Testing basic navigation and description tasks")
    print()
    
    asyncio.run(run_simple_test())
