#!/usr/bin/env python3
"""
Test interactive task to determine if agent can click despite 0 detected elements.

This is Gemini's recommended "decisive experiment" to understand:
1. Can the agent act without positive detection from OmniDetectParser?
2. Is there a fallback mechanism (VLM guessing coordinates)?
3. Is OmniDetectParser a hard dependency for interaction?

The outcome will determine our immediate path forward.
"""

import os
import asyncio
from datetime import datetime
from pathlib import Path

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError:
    CUA_AVAILABLE = False
    print("❌ CUA framework not available. Install with: pip install cua-agent[omni]")
    exit(1)


class InteractiveTaskTester:
    """Test interactive tasks to understand OmniDetectParser dependency."""
    
    def __init__(self, model_provider: str = "openai", model_name: str = "gpt-4o"):
        self.model_provider = model_provider.upper()
        self.model_name = model_name
        self.agent = None
        self.computer = None
        
        # Validate provider
        self._validate_provider()
    
    def _validate_provider(self):
        """Validate that the specified provider is available."""
        provider_map = {
            "OPENAI": ("OPENAI_API_KEY", LLMProvider.OPENAI),
            "ANTHROPIC": ("ANTHROPIC_API_KEY", LLMProvider.ANTHROPIC),
            "OLLAMA": (None, LLMProvider.OLLAMA),
        }
        
        if self.model_provider not in provider_map:
            raise ValueError(f"Unsupported provider: {self.model_provider}")
        
        api_key_env, self.llm_provider = provider_map[self.model_provider]
        
        if api_key_env and not os.getenv(api_key_env):
            raise ValueError(f"Missing {api_key_env} environment variable")
    
    async def run_decisive_experiment(self):
        """Run Gemini's decisive experiment: test simple interactive task."""
        print("🎯 Decisive Experiment: Interactive Task Testing")
        print("Testing if agent can interact despite 0 detected elements")
        print("=" * 60)
        
        async with Computer() as computer:
            self.computer = computer
            
            # Create agent with OMNI loop
            self.agent = ComputerAgent(
                computer=computer,
                loop=AgentLoop.OMNI,
                model=LLM(provider=self.llm_provider, name=self.model_name)
            )
            
            print(f"✅ OMNI agent initialized with {self.model_provider}:{self.model_name}")
            
            # Test 1: Simple click task on example.com
            await self._test_example_click()
            
            # Test 2: Simple click task on a form page
            await self._test_form_interaction()
            
            # Analyze results
            await self._analyze_interaction_results()
        
        print("\n🏁 Decisive experiment completed!")
    
    async def _test_example_click(self):
        """Test 1: Try to click the 'More information...' link on example.com."""
        print("\n🌐 Test 1: Click Link on Example.com")
        print("-" * 50)
        
        try:
            # Record starting state
            trajectories_dir = Path("trajectories")
            existing_runs = list(trajectories_dir.glob("*")) if trajectories_dir.exists() else []
            
            # Interactive task: click a link
            task = "Navigate to example.com and click the 'More information...' link"
            print(f"🤖 Task: {task}")
            print("Expected behavior:")
            print("  - Success: Agent clicks link and navigates to IANA page")
            print("  - Failure: Agent says it can't find the link or does nothing")
            
            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    print(f"   Agent: {text}")
            
            # Check for new trajectory data
            await asyncio.sleep(2)
            new_runs = list(trajectories_dir.glob("*")) if trajectories_dir.exists() else []
            new_trajectories = [run for run in new_runs if run not in existing_runs]
            
            if new_trajectories:
                latest_run = max(new_trajectories, key=lambda x: x.name)
                print(f"📁 Latest trajectory: {latest_run}")
                
                # Check for screenshots indicating navigation
                screenshots = list(latest_run.rglob("*.png"))
                print(f"📸 Screenshots captured: {len(screenshots)}")
                
                # Look for evidence of clicking
                turn_dirs = sorted([d for d in latest_run.iterdir() if d.is_dir() and d.name.startswith('turn_')])
                if len(turn_dirs) > 1:
                    print(f"🔄 Multiple turns detected: {len(turn_dirs)} (suggests interaction attempts)")
                else:
                    print("🔄 Single turn only (suggests no interaction)")
            
            # Save response for analysis
            self._save_test_response("test1_example_click", task, response_text)
            
        except Exception as e:
            print(f"❌ Example click test failed: {e}")
    
    async def _test_form_interaction(self):
        """Test 2: Try to interact with a simple form."""
        print("\n📝 Test 2: Form Interaction")
        print("-" * 50)
        
        try:
            # Interactive task: fill a form field
            task = "Navigate to httpbin.org/forms/post and type 'test' in the first input field"
            print(f"🤖 Task: {task}")
            print("Expected behavior:")
            print("  - Success: Agent types in the form field")
            print("  - Failure: Agent says it can't find the input field")
            
            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    print(f"   Agent: {text}")
            
            # Save response for analysis
            self._save_test_response("test2_form_interaction", task, response_text)
            
        except Exception as e:
            print(f"❌ Form interaction test failed: {e}")
    
    def _save_test_response(self, test_name: str, task: str, response: str):
        """Save test response to file."""
        try:
            response_dir = Path("debug_interactive")
            response_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            response_file = response_dir / f"{test_name}_{timestamp}.txt"
            
            with open(response_file, 'w') as f:
                f.write(f"Test: {test_name}\n")
                f.write(f"Timestamp: {datetime.now()}\n")
                f.write(f"Task: {task}\n")
                f.write(f"Response:\n{response}\n")
            
            print(f"💾 Response saved: {response_file}")
            
        except Exception as e:
            print(f"⚠️ Failed to save response: {e}")
    
    async def _analyze_interaction_results(self):
        """Analyze the results of interaction tests."""
        print("\n📊 Interaction Results Analysis")
        print("-" * 50)
        
        try:
            # Find the most recent trajectory
            trajectories_dir = Path("trajectories")
            if not trajectories_dir.exists():
                print("❌ No trajectories directory found")
                return
            
            runs = sorted(trajectories_dir.glob("*"), key=lambda x: x.name, reverse=True)
            if not runs:
                print("❌ No trajectory runs found")
                return
            
            latest_run = runs[0]
            print(f"📁 Analyzing latest run: {latest_run.name}")
            
            # Count turns (indicates interaction attempts)
            turn_dirs = sorted([d for d in latest_run.iterdir() if d.is_dir() and d.name.startswith('turn_')])
            print(f"🔄 Total turns: {len(turn_dirs)}")
            
            # Analyze each turn for interaction evidence
            interaction_evidence = []
            for turn_dir in turn_dirs:
                turn_name = turn_dir.name
                
                # Check for screenshots with action names
                screenshots = list(turn_dir.glob("*.png"))
                action_screenshots = [s for s in screenshots if any(action in s.name for action in ['click', 'type', 'scroll', 'key'])]
                
                if action_screenshots:
                    interaction_evidence.append(f"{turn_name}: {len(action_screenshots)} action screenshots")
                
                # Check elements.json for detected elements
                elements_file = turn_dir / "elements.json"
                if elements_file.exists():
                    with open(elements_file) as f:
                        elements = f.read().strip()
                        if elements != "[]":
                            interaction_evidence.append(f"{turn_name}: Non-empty elements detected")
            
            if interaction_evidence:
                print("✅ Evidence of interaction attempts:")
                for evidence in interaction_evidence:
                    print(f"   - {evidence}")
                print("\n🎯 CONCLUSION: Agent CAN attempt interactions despite 0 detected elements")
                print("   This suggests there's a fallback mechanism (VLM coordinate guessing)")
            else:
                print("❌ No evidence of interaction attempts found")
                print("\n🎯 CONCLUSION: OmniDetectParser appears to be a hard dependency")
                print("   Agent cannot interact without positive element detection")
            
            print(f"\n📁 Full trajectory available at: {latest_run.absolute()}")
            print("Next steps:")
            print("1. Examine screenshots to see what actually happened")
            print("2. Check API logs for interaction commands")
            print("3. Tune OmniDetectParser configuration if needed")
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")


async def main():
    """Main test function."""
    print("🚀 Interactive Task Tester")
    print("Implementing Gemini's decisive experiment")
    print("Testing agent interaction capabilities with 0 detected elements")
    print()
    
    tester = InteractiveTaskTester()
    await tester.run_decisive_experiment()


if __name__ == "__main__":
    asyncio.run(main())
