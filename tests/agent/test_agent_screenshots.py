#!/usr/bin/env python3
"""
Test agent screenshot capture following Gemini's debugging strategy.

This script implements the recommended diagnostic approach:
1. Run simple task on known-good website (example.com)
2. Inspect agent's screenshot directory
3. Analyze what the agent actually sees

This will definitively answer whether the issue is:
- VM environment (blank/black screenshots)
- Website-specific (Indeed.com issues)
- OmniDetectParser configuration
"""

import os
import asyncio
from datetime import datetime
from pathlib import Path

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError:
    CUA_AVAILABLE = False
    print("❌ CUA framework not available. Install with: pip install cua-agent[omni]")
    exit(1)


class AgentScreenshotTester:
    """Test agent screenshot capture with progressive complexity."""
    
    def __init__(self, model_provider: str = "openai", model_name: str = "gpt-4o"):
        self.model_provider = model_provider.upper()
        self.model_name = model_name
        self.agent = None
        self.computer = None
        
        # Validate provider
        self._validate_provider()
    
    def _validate_provider(self):
        """Validate that the specified provider is available."""
        provider_map = {
            "OPENAI": ("OPENAI_API_KEY", LLMProvider.OPENAI),
            "ANTHROPIC": ("ANTHROPIC_API_KEY", LLMProvider.ANTHROPIC),
            "OLLAMA": (None, LLMProvider.OLLAMA),
        }
        
        if self.model_provider not in provider_map:
            raise ValueError(f"Unsupported provider: {self.model_provider}")
        
        api_key_env, self.llm_provider = provider_map[self.model_provider]
        
        if api_key_env and not os.getenv(api_key_env):
            raise ValueError(f"Missing {api_key_env} environment variable")
    
    async def run_diagnostic_tests(self):
        """Run Gemini's recommended diagnostic sequence."""
        print("🔍 Agent Screenshot Diagnostic Tests")
        print("Following Gemini's debugging strategy:")
        print("1. Simple task on known-good website")
        print("2. Inspect agent's screenshot directory")
        print("3. Analyze what agent actually sees")
        print("=" * 60)
        
        async with Computer() as computer:
            self.computer = computer
            
            # Create agent with OMNI loop
            self.agent = ComputerAgent(
                computer=computer,
                loop=AgentLoop.OMNI,
                model=LLM(provider=self.llm_provider, name=self.model_name)
            )
            
            print(f"✅ OMNI agent initialized with {self.model_provider}:{self.model_name}")
            print(f"📁 Agent screenshot directory: {self.agent.screenshot_dir}")

            # Debug: Check agent attributes
            print("\n🔍 Agent Attributes Debug:")
            agent_attrs = [attr for attr in dir(self.agent) if not attr.startswith('_')]
            for attr in agent_attrs:
                try:
                    value = getattr(self.agent, attr)
                    if not callable(value):
                        print(f"   - {attr}: {value}")
                except:
                    print(f"   - {attr}: <unable to access>")

            # Check if we can find any screenshot-related paths
            if hasattr(self.agent, 'log_dir') and self.agent.log_dir:
                print(f"📁 Agent log directory: {self.agent.log_dir}")
                log_path = Path(self.agent.log_dir)
                if log_path.exists():
                    print(f"   Log directory contents: {list(log_path.iterdir())}")

            # Test 1: Simple static website
            await self._test_example_com()
            
            # Test 2: Interactive elements (if Test 1 succeeds)
            await self._test_interactive_site()
            
            # Test 3: Indeed.com (if previous tests succeed)
            await self._test_indeed_com()
            
            # Analyze all screenshots
            await self._analyze_screenshots()
        
        print("\n🏁 Diagnostic tests completed!")
    
    async def _test_example_com(self):
        """Test 1: Simple task on example.com (Gemini's recommendation)."""
        print("\n🌐 Test 1: Example.com (Simple Static Site)")
        print("-" * 50)
        
        try:
            # Handle case where screenshot_dir is None
            if self.agent.screenshot_dir:
                screenshot_dir = Path(self.agent.screenshot_dir)
                existing_files = list(screenshot_dir.glob("*.png")) if screenshot_dir.exists() else []
                print(f"📸 Screenshots before task: {len(existing_files)}")
            else:
                print("⚠️ Agent screenshot_dir is None - will check log_dir and trajectories")
                screenshot_dir = None
                existing_files = []
            
            # Simple, non-interactive task
            task = "Navigate to example.com and describe the contents of the page. What is the main heading?"
            print(f"🤖 Task: {task}")
            
            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    print(f"   Agent: {text}")
            
            # Check for new screenshots in multiple possible locations
            await asyncio.sleep(2)  # Give time for files to be written

            # Check screenshot_dir if available
            if screenshot_dir and screenshot_dir.exists():
                new_files = list(screenshot_dir.glob("*.png"))
                new_screenshots = [f for f in new_files if f not in existing_files]
                print(f"📸 New screenshots in screenshot_dir: {len(new_screenshots)}")
                for screenshot in new_screenshots:
                    print(f"   - {screenshot.name}")

            # Check log_dir for screenshots
            if hasattr(self.agent, 'log_dir') and self.agent.log_dir:
                log_dir = Path(self.agent.log_dir)
                if log_dir.exists():
                    log_screenshots = list(log_dir.rglob("*.png"))
                    print(f"📸 Screenshots in log_dir: {len(log_screenshots)}")
                    for screenshot in log_screenshots:
                        print(f"   - {screenshot}")

            # Check trajectories directory
            trajectories_dir = Path("trajectories")
            if trajectories_dir.exists():
                traj_screenshots = list(trajectories_dir.rglob("*.png"))
                print(f"📸 Screenshots in trajectories: {len(traj_screenshots)}")
                for screenshot in traj_screenshots:
                    print(f"   - {screenshot}")
            
            # Save response
            self._save_test_response("test1_example_com", task, response_text)
            
        except Exception as e:
            print(f"❌ Example.com test failed: {e}")
    
    async def _test_interactive_site(self):
        """Test 2: Site with clear interactive elements."""
        print("\n🎯 Test 2: Interactive Elements Test")
        print("-" * 50)
        
        try:
            screenshot_dir = Path(self.agent.screenshot_dir)
            existing_files = list(screenshot_dir.glob("*.png")) if screenshot_dir.exists() else []
            
            # Test with a form page
            task = "Navigate to httpbin.org/forms/post and describe what form elements you can see"
            print(f"🤖 Task: {task}")
            
            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    print(f"   Agent: {text}")
            
            await asyncio.sleep(2)
            new_files = list(screenshot_dir.glob("*.png")) if screenshot_dir.exists() else []
            new_screenshots = [f for f in new_files if f not in existing_files]
            
            print(f"📸 New screenshots: {len(new_screenshots)}")
            for screenshot in new_screenshots:
                print(f"   - {screenshot.name}")
            
            self._save_test_response("test2_interactive", task, response_text)
            
        except Exception as e:
            print(f"❌ Interactive test failed: {e}")
    
    async def _test_indeed_com(self):
        """Test 3: Indeed.com (the actual target)."""
        print("\n💼 Test 3: Indeed.com (Actual Target)")
        print("-" * 50)
        
        try:
            screenshot_dir = Path(self.agent.screenshot_dir)
            existing_files = list(screenshot_dir.glob("*.png")) if screenshot_dir.exists() else []
            
            # Simple navigation task (no interaction yet)
            task = "Navigate to indeed.com and describe what you see on the page"
            print(f"🤖 Task: {task}")
            
            response_text = ""
            async for response in self.agent.run(task):
                text = response.get("text", "")
                response_text += text
                if text.strip():
                    print(f"   Agent: {text}")
            
            await asyncio.sleep(2)
            new_files = list(screenshot_dir.glob("*.png")) if screenshot_dir.exists() else []
            new_screenshots = [f for f in new_files if f not in existing_files]
            
            print(f"📸 New screenshots: {len(new_screenshots)}")
            for screenshot in new_screenshots:
                print(f"   - {screenshot.name}")
            
            self._save_test_response("test3_indeed", task, response_text)
            
        except Exception as e:
            print(f"❌ Indeed test failed: {e}")
    
    def _save_test_response(self, test_name: str, task: str, response: str):
        """Save test response to file."""
        try:
            response_dir = Path("debug_responses")
            response_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            response_file = response_dir / f"{test_name}_{timestamp}.txt"
            
            with open(response_file, 'w') as f:
                f.write(f"Test: {test_name}\n")
                f.write(f"Timestamp: {datetime.now()}\n")
                f.write(f"Task: {task}\n")
                f.write(f"Response:\n{response}\n")
            
            print(f"💾 Response saved: {response_file}")
            
        except Exception as e:
            print(f"⚠️ Failed to save response: {e}")
    
    async def _analyze_screenshots(self):
        """Analyze all screenshots captured during tests."""
        print("\n📊 Screenshot Analysis")
        print("-" * 50)

        try:
            all_screenshots = []

            # Check multiple possible locations for screenshots
            locations_to_check = []

            # Add screenshot_dir if available
            if self.agent.screenshot_dir:
                locations_to_check.append(("screenshot_dir", Path(self.agent.screenshot_dir)))

            # Add log_dir if available
            if hasattr(self.agent, 'log_dir') and self.agent.log_dir:
                locations_to_check.append(("log_dir", Path(self.agent.log_dir)))

            # Add trajectories directory
            locations_to_check.append(("trajectories", Path("trajectories")))

            for location_name, location_path in locations_to_check:
                if location_path.exists():
                    screenshots = list(location_path.rglob("*.png"))
                    if screenshots:
                        print(f"\n📸 Screenshots in {location_name} ({location_path}):")
                        for screenshot in sorted(screenshots):
                            stat = screenshot.stat()
                            size_kb = stat.st_size / 1024
                            mod_time = datetime.fromtimestamp(stat.st_mtime)
                            print(f"   - {screenshot.relative_to(location_path)} ({size_kb:.1f} KB, {mod_time.strftime('%H:%M:%S')})")
                        all_screenshots.extend(screenshots)
                    else:
                        print(f"\n📁 {location_name} ({location_path}): No screenshots found")
                else:
                    print(f"\n📁 {location_name} ({location_path}): Directory doesn't exist")

            if all_screenshots:
                print(f"\n✅ Total screenshots found: {len(all_screenshots)}")
                print("\nNext steps:")
                print("1. Open the screenshots to see what the agent captured")
                print("2. Check if images show:")
                print("   - Blank/black screen → VM display issue")
                print("   - Correct websites → OmniDetectParser issue")
                print("   - Desktop/lock screen → Browser launch issue")
            else:
                print("\n❌ No screenshots found in any location")
                print("This suggests screenshots aren't being saved or are in an unexpected location")

        except Exception as e:
            print(f"❌ Screenshot analysis failed: {e}")


async def main():
    """Main diagnostic function."""
    print("🚀 Agent Screenshot Diagnostic")
    print("Implementing Gemini's recommended debugging strategy")
    print()
    
    tester = AgentScreenshotTester()
    await tester.run_diagnostic_tests()


if __name__ == "__main__":
    asyncio.run(main())
