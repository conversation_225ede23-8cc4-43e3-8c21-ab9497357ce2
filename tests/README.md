# CUA Agent Tests

This directory contains all tests for the CUA Agent project, organized by category.

## Directory Structure

```
tests/
├── agent/           # Agent behavior and functionality tests
├── integration/     # Integration tests (video recording, capture, etc.)
├── debug/          # Debug utilities and threshold testing
├── manual/         # Manual testing utilities
├── unit/           # Unit tests for individual components
└── fixtures/       # Test data and fixtures
```

## Test Categories

### Agent Tests (`tests/agent/`)
- `simple_recorded_test.py` - Basic agent test with session recording
- `test_with_recording.py` - Comprehensive agent test suite
- `test_agent_screenshots.py` - Agent screenshot capture testing
- `test_interactive_task.py` - Interactive task testing

### Integration Tests (`tests/integration/`)
- `test_video_recording.py` - Video recording functionality
- `test_basic_capture.py` - Basic screen capture testing

### Debug Tests (`tests/debug/`)
- `debug_omni_detection.py` - OMNI detection debugging
- `test_detector_thresholds.py` - Detection threshold testing
- `test_live_detection.py` - Live detection debugging
- `debug_basic/` - Basic debugging utilities
- `debug_thresholds/` - Threshold test results

### Manual Tests (`tests/manual/`)
- `view_sessions.py` - Session recording viewer

## Running Tests

### Quick Agent Test
```bash
cd tests/agent
python simple_recorded_test.py
```

### Full Agent Test Suite
```bash
cd tests/agent
python test_with_recording.py
```

### Video Recording Test
```bash
cd tests/integration
python test_video_recording.py
```

### View Test Sessions
```bash
cd tests/manual
python view_sessions.py
```

## Test Requirements

- CUA framework installed (`pip install cua-agent[omni]`)
- LUME VM running (use `./infra/lume/start_persistent_vm.sh`)
- Environment variables set (see `.env.example`)
- ffmpeg installed for video recording (`brew install ffmpeg`)

## Test Output

- Session recordings: `session_recordings/`
- Agent trajectories: `trajectories/`
- Screenshots: `output/screenshots/`
- Debug images: `tests/debug/debug_thresholds/`
