#!/usr/bin/env python3
"""
CUA Agent Test Runner

Provides a simple interface to run different categories of tests.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return success status."""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {cmd}")
        print(f"   Error: {e}")
        return False

def main():
    """Main test runner."""
    print("🧪 CUA Agent Test Runner")
    print("=" * 50)
    
    # Get the tests directory
    tests_dir = Path(__file__).parent
    project_root = tests_dir.parent
    
    print("\nAvailable test categories:")
    print("1. Quick Agent Test (simple_recorded_test.py)")
    print("2. Full Agent Test Suite (test_with_recording.py)")
    print("3. Video Recording Test")
    print("4. Debug Detection Tests")
    print("5. View Test Sessions")
    print("6. All Agent Tests")
    print("0. Exit")
    
    while True:
        choice = input("\nSelect test category (0-6): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            break
            
        elif choice == "1":
            print("\n🚀 Running Quick Agent Test...")
            success = run_command("python simple_recorded_test.py", cwd=tests_dir / "agent")
            if success:
                print("✅ Quick agent test completed!")
            
        elif choice == "2":
            print("\n🚀 Running Full Agent Test Suite...")
            success = run_command("python test_with_recording.py", cwd=tests_dir / "agent")
            if success:
                print("✅ Full agent test suite completed!")
            
        elif choice == "3":
            print("\n🎥 Running Video Recording Test...")
            success = run_command("python test_video_recording.py", cwd=tests_dir / "integration")
            if success:
                print("✅ Video recording test completed!")
            
        elif choice == "4":
            print("\n🔍 Running Debug Detection Tests...")
            print("Available debug tests:")
            print("  a. OMNI Detection Debug")
            print("  b. Threshold Testing")
            print("  c. Live Detection")
            
            debug_choice = input("Select debug test (a-c): ").strip().lower()
            
            if debug_choice == "a":
                success = run_command("python debug_omni_detection.py", cwd=tests_dir / "debug")
            elif debug_choice == "b":
                success = run_command("python test_detector_thresholds.py", cwd=tests_dir / "debug")
            elif debug_choice == "c":
                success = run_command("python test_live_detection.py", cwd=tests_dir / "debug")
            else:
                print("❌ Invalid debug test selection")
                continue
                
            if success:
                print("✅ Debug test completed!")
            
        elif choice == "5":
            print("\n📊 Opening Session Viewer...")
            success = run_command("python view_sessions.py", cwd=tests_dir / "manual")
            
        elif choice == "6":
            print("\n🚀 Running All Agent Tests...")
            
            print("1/2 Running quick agent test...")
            success1 = run_command("python simple_recorded_test.py", cwd=tests_dir / "agent")
            
            if success1:
                print("2/2 Running full agent test suite...")
                success2 = run_command("python test_with_recording.py", cwd=tests_dir / "agent")
                
                if success2:
                    print("✅ All agent tests completed successfully!")
                else:
                    print("❌ Full agent test failed")
            else:
                print("❌ Quick agent test failed, skipping full test")
            
        else:
            print("❌ Invalid selection. Please choose 0-6.")
            continue
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
