#!/usr/bin/env python3
"""
Test script to check what models are available through OpenRouter
and if any have computer-use capabilities.
"""

import os
import asyncio
import requests
from dotenv import load_dotenv

load_dotenv()


def test_openrouter_models():
    """Test what models are available through OpenRouter."""
    print("🔍 Testing OpenRouter Model Availability")
    print("=" * 50)
    
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found")
        return False
    
    # Test different model names that might have computer use
    models_to_test = [
        # Anthropic models
        "anthropic/claude-3.5-sonnet",
        "anthropic/claude-3.5-sonnet:beta",
        "anthropic/claude-3-5-sonnet-20241022",
        "anthropic/claude-3-haiku",
        
        # OpenAI models
        "openai/gpt-4o",
        "openai/gpt-4-turbo",
        "openai/computer-use-preview",
        "computer-use-preview",
        "computer-use-preview-2025-03-11",
        
        # Other possibilities
        "anthropic/claude-computer-use",
        "openai/gpt-4o-computer-use",
    ]
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/nebitessema/cua_agent",
        "X-Title": "CUA Agent Test"
    }
    
    working_models = []
    
    for model in models_to_test:
        print(f"\n🧠 Testing model: {model}")
        
        # Simple test request
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "Hello, can you help with computer tasks?"}
            ],
            "max_tokens": 50
        }
        
        try:
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                print(f"✅ {model} - Working!")
                print(f"   Response: {content[:100]}...")
                working_models.append(model)
                
            elif response.status_code == 404:
                print(f"❌ {model} - Model not found")
            elif response.status_code == 400:
                error_msg = response.json().get('error', {}).get('message', 'Unknown error')
                print(f"⚠️ {model} - Bad request: {error_msg}")
            else:
                print(f"⚠️ {model} - HTTP {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ {model} - Error: {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Working models: {len(working_models)}")
    for model in working_models:
        print(f"   ✅ {model}")
    
    return len(working_models) > 0


def test_computer_use_capabilities():
    """Test if any models support computer use features."""
    print(f"\n🖥️ Testing Computer Use Capabilities")
    print("=" * 50)
    
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found")
        return False
    
    # Test with Claude 3.5 Sonnet (most likely to have computer use)
    model = "anthropic/claude-3.5-sonnet"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/nebitessema/cua_agent",
        "X-Title": "CUA Agent Test"
    }
    
    # Test with computer use tools
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "Can you take a screenshot of the current screen?"}
        ],
        "tools": [
            {
                "type": "computer_20241022",
                "computer_20241022": {
                    "display_width_px": 1024,
                    "display_height_px": 768,
                    "display_number": 1
                }
            }
        ],
        "max_tokens": 100
    }
    
    try:
        print(f"🧠 Testing {model} with computer use tools...")
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Computer use tools accepted!")
            print(f"   Response: {result}")
            return True
        else:
            print(f"⚠️ Computer use tools not supported: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing computer use: {e}")
        return False


async def main():
    """Run OpenRouter model tests."""
    print("🚀 OpenRouter Model Discovery")
    print("=" * 60)
    
    # Test 1: Available models
    models_available = test_openrouter_models()
    
    # Test 2: Computer use capabilities
    if models_available:
        computer_use_available = test_computer_use_capabilities()
    else:
        computer_use_available = False
    
    # Summary
    print(f"\n🎉 OpenRouter Test Summary")
    print("=" * 40)
    
    if models_available:
        print("✅ OpenRouter API working")
        print("✅ Multiple models available")
        
        if computer_use_available:
            print("✅ Computer use capabilities detected!")
            print("💡 You might be able to use OpenRouter for CUA tasks")
        else:
            print("⚠️ Computer use capabilities not detected")
            print("💡 OpenRouter models may not support computer use tools")
    else:
        print("❌ OpenRouter API issues")
        print("💡 Check your API key and try again")
    
    return models_available


if __name__ == "__main__":
    success = asyncio.run(main())
    print(f"\n🔗 Useful links:")
    print(f"   • OpenRouter Models: https://openrouter.ai/models")
    print(f"   • OpenRouter Docs: https://openrouter.ai/docs")
    print(f"   • Anthropic Computer Use: https://docs.anthropic.com/en/docs/build-with-claude/computer-use")
    
    exit(0 if success else 1)
