#!/usr/bin/env python3
"""
Test script for OMNI-powered Indeed adapter.

This script tests the OMNI Indeed adapter functionality including:
- OMNI loop initialization with available models
- Task-driven job collection workflow
- Natural language navigation and data extraction
"""

import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from job_collector.adapters.omni_indeed_adapter import OMNIIndeedAdapter
from job_collector.core.models import SearchParameters, JobType, ExperienceLevel, RemoteType
from job_collector.storage.json_storage import JSONStorage

# Load environment variables
load_dotenv()


async def test_omni_indeed_basic():
    """Test basic OMNI Indeed adapter functionality."""
    print("🤖 Testing OMNI Indeed Adapter")
    print("=" * 50)
    
    try:
        # Determine which provider to use based on available API keys
        if os.getenv('OPENAI_API_KEY'):
            provider = "openai"
            model = "gpt-4o"
            print(f"✅ Using OpenAI: {model}")
        elif os.getenv('ANTHROPIC_API_KEY'):
            provider = "anthropic"
            model = "claude-3-5-sonnet-20241022"
            print(f"✅ Using Anthropic: {model}")
        else:
            provider = "ollama"
            model = "llama3.2-vision"
            print(f"✅ Using Ollama: {model} (local)")
        
        # Initialize adapter
        print("🔧 Initializing OMNI Indeed adapter...")
        adapter = OMNIIndeedAdapter(model_provider=provider, model_name=model)
        print(f"✅ Adapter initialized: {adapter.platform_name}")
        
        # Test authentication (should always succeed for Indeed)
        print("🔐 Testing authentication...")
        auth_result = adapter.authenticate({})
        print(f"✅ Authentication: {'Success' if auth_result else 'Failed'}")
        
        # Test rate limits
        print("⏱️  Testing rate limits...")
        rate_limits = adapter.get_rate_limits()
        print(f"✅ Rate limits: {rate_limits}")
        
        return adapter
        
    except Exception as e:
        print(f"❌ Basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_omni_indeed_search(adapter):
    """Test OMNI Indeed job search functionality."""
    if not adapter:
        print("⚠️ Skipping search test - adapter not available")
        return None
    
    print(f"\n🔍 Testing OMNI Indeed Job Search")
    print("=" * 50)
    
    try:
        # Create search parameters
        search_params = SearchParameters(
            keywords=["python", "software engineer"],
            location="San Francisco, CA",
            job_types=[JobType.FULL_TIME],
            experience_levels=[ExperienceLevel.MID_LEVEL],
            max_results=3,  # Small number for testing
            posted_since_days=7
        )
        
        print(f"📋 Search Parameters:")
        print(f"   Keywords: {search_params.keywords}")
        print(f"   Location: {search_params.location}")
        print(f"   Job Types: {[jt.value for jt in search_params.job_types]}")
        print(f"   Max Results: {search_params.max_results}")
        print(f"   Posted Since: {search_params.posted_since_days} days")
        
        # Perform search
        print(f"\n🚀 Starting OMNI job search...")
        print("This will:")
        print("1. Use OMNI loop (OmniParser + VLM) for navigation")
        print("2. Navigate to Indeed.com using natural language")
        print("3. Perform search using task-driven workflow")
        print("4. Apply filters with natural language commands")
        print("5. Extract job data using agent reasoning")
        print()
        
        result = await adapter.search_jobs(search_params)
        
        # Analyze results
        print(f"\n📊 Search Results:")
        print(f"   Success: {result.success}")
        print(f"   Total Collected: {result.total_collected}")
        print(f"   Pages Processed: {result.pages_processed}")
        print(f"   Errors: {len(result.errors)}")
        
        if result.errors:
            print(f"   Error Details: {result.errors}")
        
        # Display sample jobs
        if result.job_listings:
            print(f"\n📋 Sample Job Listings:")
            for i, job in enumerate(result.job_listings):
                print(f"\n   Job {i+1}:")
                print(f"     Title: {job.title}")
                print(f"     Company: {job.company}")
                print(f"     Location: {job.location}")
                print(f"     Remote Type: {job.remote_type.value}")
                print(f"     URL: {job.url}")
                if job.salary_min or job.salary_max:
                    print(f"     Salary: ${job.salary_min or 0:,} - ${job.salary_max or 0:,}")
                print(f"     Description: {job.description[:100]}...")
        
        return result
        
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_storage_integration(result):
    """Test storing OMNI search results."""
    if not result or not result.job_listings:
        print("⚠️ Skipping storage test - no results to store")
        return
    
    print(f"\n💾 Testing Storage Integration")
    print("=" * 50)
    
    try:
        # Initialize storage
        storage_path = "./output/test_omni_indeed_jobs.json"
        storage = JSONStorage(storage_path)
        
        # Store results
        print(f"💾 Storing {len(result.job_listings)} jobs...")
        for job in result.job_listings:
            storage.store_job(job)
        
        # Test retrieval
        print("🔍 Testing job retrieval...")
        stored_jobs = storage.get_all_jobs()
        print(f"✅ Retrieved {len(stored_jobs)} jobs from storage")
        
        # Test search
        print("🔍 Testing job search...")
        python_jobs = storage.search_stored_jobs({"keywords": ["python"]})
        print(f"✅ Found {len(python_jobs)} Python jobs")
        
        # Export results
        print("📤 Testing data export...")
        export_path = storage.export_data("json")
        print(f"✅ Exported data to: {export_path}")
        
        return storage
        
    except Exception as e:
        print(f"❌ Storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_omni_advantages():
    """Demonstrate OMNI loop advantages."""
    print(f"\n🎯 OMNI Loop Advantages")
    print("=" * 50)
    
    advantages = [
        "✅ Model Access: Works with any VLM (OpenAI, Anthropic, local models)",
        "✅ Two-Stage Architecture: OmniParser (detection) + VLM (reasoning)",
        "✅ Robust Navigation: Natural language commands for UI interaction",
        "✅ Flexible Providers: Switch between OpenAI, Anthropic, Ollama easily",
        "✅ Cost Control: Use local models for development, cloud for production",
        "✅ Resilient to UI Changes: Semantic understanding vs brittle selectors",
        "✅ Task-Driven Workflow: Structured approach for complex automation",
        "✅ Human-Like Interaction: Real browser automation with visual reasoning"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")
    
    print(f"\n🔄 OMNI vs Other Approaches:")
    print(f"   • OMNI: OmniParser + Any VLM (accessible, flexible)")
    print(f"   • OpenAI Loop: computer-use-preview only (limited access)")
    print(f"   • Anthropic Loop: Claude computer-use only (beta access)")
    print(f"   • Selenium: Brittle selectors, bot detection issues")


async def main():
    """Run the complete OMNI Indeed adapter test."""
    print("🚀 OMNI Indeed Adapter Test Suite")
    print("=" * 60)
    print("This test will:")
    print("1. Initialize OMNI Indeed adapter with available models")
    print("2. Test basic functionality")
    print("3. Perform actual job search using OMNI loop")
    print("4. Store and analyze results")
    print("5. Demonstrate OMNI advantages")
    print()
    
    # Check environment
    has_openai = bool(os.getenv('OPENAI_API_KEY'))
    has_anthropic = bool(os.getenv('ANTHROPIC_API_KEY'))
    
    if not has_openai and not has_anthropic:
        print("⚠️ No cloud API keys found. Will attempt to use local Ollama models.")
        print("💡 For best results, set OPENAI_API_KEY or ANTHROPIC_API_KEY")
    
    # Test 1: Basic functionality
    adapter = await test_omni_indeed_basic()
    
    if adapter:
        # Test 2: Job search
        result = await test_omni_indeed_search(adapter)
        
        if result and result.success:
            # Test 3: Storage
            storage = test_storage_integration(result)
            
            # Test 4: Demonstrate advantages
            test_omni_advantages()
            
            print(f"\n🎉 OMNI Indeed Adapter Test Complete!")
            print(f"✅ Successfully collected {result.total_collected} jobs using OMNI loop")
            print(f"✅ Task-driven workflow working")
            print(f"✅ Data stored and analyzed successfully")
            
            return True
        else:
            print(f"\n❌ Search test failed")
            return False
    else:
        print(f"\n❌ Basic test failed")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
