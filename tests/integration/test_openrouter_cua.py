#!/usr/bin/env python3
"""
Test script for OpenRouter integration with CUA.

This script tests OpenRouter as a provider for the CUA framework,
which can give access to many models including Claude and GPT variants
that might have computer-use capabilities.
"""

import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


async def test_openrouter_providers():
    """Test available OpenRouter providers and models."""
    print("🔄 Testing OpenRouter Integration")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing CUA imports...")
        from computer import Computer
        from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
        print("✅ CUA modules imported successfully")
        
        # Check API key
        openrouter_key = os.getenv('OPENROUTER_API_KEY')
        if not openrouter_key:
            print("❌ OPENROUTER_API_KEY not found in environment")
            print("💡 Set your OpenRouter API key: export OPENROUTER_API_KEY=your_key_here")
            return False
        
        print(f"✅ OpenRouter API key found: {openrouter_key[:10]}...")
        
        # Test Computer initialization
        print("💻 Testing Computer initialization...")
        computer = Computer(os_type="macos")
        print("✅ Computer initialized")
        
        # Test different OpenRouter models
        models_to_test = [
            "anthropic/claude-3.5-sonnet",
            "anthropic/claude-3-haiku",
            "openai/gpt-4o",
            "openai/gpt-4-turbo",
            "meta-llama/llama-3.1-8b-instruct",
        ]
        
        for model_name in models_to_test:
            print(f"\n🧠 Testing model: {model_name}")
            try:
                # Create LLM with OpenRouter (OAICOMPAT provider)
                llm = LLM(provider=LLMProvider.OAICOMPAT, name=model_name)
                print(f"✅ LLM created for {model_name}")
                
                # Create agent
                agent = ComputerAgent(
                    computer=computer,
                    model=llm,
                    loop=AgentLoop.OPENAI  # Use OpenAI loop for OpenAI-compatible APIs
                )
                print(f"✅ Agent created for {model_name}")
                
                # Test simple task
                print(f"🎯 Testing simple task with {model_name}...")
                task = "Take a screenshot of the current screen"
                
                async for result in agent.run(task):
                    print(f"Result from {model_name}: {result}")
                    break  # Just get first result
                
                print(f"✅ {model_name} test completed")
                break  # If one works, that's enough for testing
                
            except Exception as e:
                print(f"⚠️ {model_name} failed: {e}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ OpenRouter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_openrouter_job_search():
    """Test OpenRouter with job search functionality."""
    print(f"\n🔍 Testing OpenRouter Job Search")
    print("=" * 50)
    
    try:
        # Add src to path for imports
        sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))
        
        from job_collector.adapters.cua_indeed_adapter import CUAIndeedAdapter
        from job_collector.core.models import SearchParameters, JobType
        
        # Initialize adapter (will auto-detect OpenRouter)
        print("🔧 Initializing CUA Indeed adapter with OpenRouter...")
        adapter = CUAIndeedAdapter(headless=False)
        
        # Create simple search parameters
        search_params = SearchParameters(
            keywords=["python"],
            location="San Francisco, CA",
            job_types=[JobType.FULL_TIME],
            max_results=3,  # Small number for testing
            posted_since_days=7
        )
        
        print(f"📋 Search Parameters:")
        print(f"   Keywords: {search_params.keywords}")
        print(f"   Location: {search_params.location}")
        print(f"   Max Results: {search_params.max_results}")
        
        # Perform search
        print(f"\n🚀 Starting OpenRouter-powered job search...")
        result = await adapter.search_jobs(search_params)
        
        # Analyze results
        print(f"\n📊 Search Results:")
        print(f"   Success: {result.success}")
        print(f"   Total Collected: {result.total_collected}")
        print(f"   Errors: {len(result.errors)}")
        
        if result.errors:
            print(f"   Error Details: {result.errors}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ OpenRouter job search test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_openrouter_info():
    """Show information about OpenRouter and available models."""
    print(f"\n📚 OpenRouter Information")
    print("=" * 50)
    
    info = [
        "🔄 OpenRouter provides access to multiple AI models through a single API",
        "🧠 Available models include Claude, GPT, Llama, and many others",
        "💰 Pay-per-use pricing with competitive rates",
        "🔑 Single API key for all models",
        "🌐 OpenAI-compatible API format",
        "",
        "🎯 Popular models for computer use:",
        "   • anthropic/claude-3.5-sonnet (recommended)",
        "   • anthropic/claude-3-haiku (faster, cheaper)",
        "   • openai/gpt-4o (if available)",
        "   • openai/gpt-4-turbo",
        "",
        "🔗 Get your API key at: https://openrouter.ai/",
        "📖 Model list: https://openrouter.ai/models",
    ]
    
    for line in info:
        print(f"   {line}")


async def main():
    """Run the OpenRouter CUA test."""
    print("🚀 OpenRouter CUA Integration Test")
    print("=" * 60)
    
    # Show OpenRouter info
    show_openrouter_info()
    
    # Check if OpenRouter API key is available
    if not os.getenv('OPENROUTER_API_KEY'):
        print("\n❌ OPENROUTER_API_KEY not found")
        print("💡 To test OpenRouter:")
        print("   1. Get an API key from https://openrouter.ai/")
        print("   2. Set it in your .env file: OPENROUTER_API_KEY=your_key_here")
        print("   3. Run this test again")
        return False
    
    # Test 1: Basic OpenRouter functionality
    print("\n" + "="*60)
    success1 = await test_openrouter_providers()
    
    # Test 2: Job search with OpenRouter
    if success1:
        print("\n" + "="*60)
        success2 = await test_openrouter_job_search()
    else:
        success2 = False
    
    # Summary
    print(f"\n🎉 OpenRouter CUA Test Summary")
    print("=" * 40)
    
    if success1 and success2:
        print("✅ OpenRouter integration working!")
        print("✅ CUA can use OpenRouter models")
        print("✅ Job search adapter supports OpenRouter")
        print("\n💡 You now have access to multiple AI models through OpenRouter!")
    elif success1:
        print("✅ OpenRouter basic integration working")
        print("⚠️ Job search needs debugging")
    else:
        print("❌ OpenRouter integration needs setup")
        print("💡 Check your API key and try again")
    
    return success1 and success2


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
