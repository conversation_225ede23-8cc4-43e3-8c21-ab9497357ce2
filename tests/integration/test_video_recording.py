#!/usr/bin/env python3
"""
Test video recording functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.job_collector.utils.session_recorder import SessionRecorder


async def test_video_recording():
    """Test video recording for 10 seconds."""
    print("🎥 Testing video recording for 10 seconds...")
    print("Move your mouse around or do something visible on screen!")
    
    with SessionRecorder("video_test", record_video=True) as recorder:
        recorder.add_metadata("test_type", "video_recording_test")
        
        # Record for 10 seconds
        print("📹 Recording started...")
        await asyncio.sleep(10)
        print("📹 Recording will stop when context exits...")
    
    print("✅ Video recording test completed!")


if __name__ == "__main__":
    asyncio.run(test_video_recording())
