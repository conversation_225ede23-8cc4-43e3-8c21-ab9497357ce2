#!/usr/bin/env python3
"""
Test script to verify Phase 1 completion.

This script tests that all core components can be imported and instantiated,
confirming that the foundation is properly implemented.
"""

import sys
from datetime import datetime

def test_imports():
    """Test that all core components can be imported."""
    print("Testing imports...")
    
    try:
        # Test core model imports
        from job_collector.core.models import JobListing, SearchParameters, CollectionResult
        from job_collector.core.models import JobType, ExperienceLevel, RemoteType
        print("✅ Core models imported successfully")
        
        # Test configuration import
        from job_collector.core.config import JobCollectorConfig
        print("✅ Configuration system imported successfully")
        
        # Test interface imports
        from job_collector.core.interfaces import DataSourceAdapter, DataExtractor, ResultsAnalyzer
        print("✅ Abstract interfaces imported successfully")
        
        # Test exception imports
        from job_collector.core.exceptions import JobCollectorError, DataSourceError
        print("✅ Exception classes imported successfully")
        
        # Test utility imports
        from job_collector.core.utils import generate_job_id, clean_text, extract_salary_range
        print("✅ Utility functions imported successfully")
        
        # Test collector imports
        from job_collector.collectors.job_collector import JobCollector
        from job_collector.collectors.base_analyzer import BaseResultsAnalyzer
        print("✅ Collector implementations imported successfully")
        
        # Test main package import
        from job_collector import JobListing, JobCollector, JobCollectorConfig
        print("✅ Main package imports working")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of core components."""
    print("\nTesting basic functionality...")
    
    try:
        from job_collector import JobListing, SearchParameters, CollectionResult
        from job_collector import JobCollectorConfig, JobCollector
        from job_collector.collectors.base_analyzer import BaseResultsAnalyzer
        from job_collector.core.models import JobType, ExperienceLevel, RemoteType
        
        # Test JobListing creation
        job = JobListing(
            id="test-123",
            title="Software Engineer",
            company="Test Company",
            platform="test",
            url="https://example.com/job/123",
            location="San Francisco, CA",
            remote_type=RemoteType.HYBRID,
            job_type=JobType.FULL_TIME,
            experience_level=ExperienceLevel.MID_LEVEL,
            salary_min=80000,
            salary_max=120000,
            required_skills=["Python", "JavaScript"],
            description="Looking for a Python developer with JavaScript experience"
        )
        print("✅ JobListing creation successful")
        
        # Test JobListing serialization
        job_dict = job.to_dict()
        job_from_dict = JobListing.from_dict(job_dict)
        assert job.id == job_from_dict.id
        print("✅ JobListing serialization/deserialization working")
        
        # Test SearchParameters
        search_params = SearchParameters(
            keywords=["python", "software engineer"],
            location="San Francisco",
            job_types=[JobType.FULL_TIME],
            experience_levels=[ExperienceLevel.MID_LEVEL],
            min_salary=70000,
            max_results=50
        )
        print("✅ SearchParameters creation successful")
        
        # Test CollectionResult
        result = CollectionResult(
            search_parameters=search_params,
            platform="test"
        )
        result.add_job(job)
        assert result.total_collected == 1
        print("✅ CollectionResult functionality working")
        
        # Test Configuration
        config = JobCollectorConfig()
        config.log_level = "DEBUG"
        config.output_directory = "./test_output"
        
        # Test configuration validation
        errors = config.validate()
        print(f"✅ Configuration validation working (errors: {len(errors)})")
        
        # Test JobCollector initialization
        collector = JobCollector(config)
        assert len(collector.get_registered_platforms()) == 0
        print("✅ JobCollector initialization successful")
        
        # Test BaseResultsAnalyzer
        analyzer = BaseResultsAnalyzer()
        
        # Test filtering
        jobs = [job]
        try:
            filtered = analyzer.filter_results(jobs, {"keywords": ["python"]})
            assert len(filtered) == 1
            print("✅ BaseResultsAnalyzer filtering working")
        except Exception as e:
            print(f"❌ Filter test failed: {e}")
            raise

        # Test insights extraction
        try:
            insights = analyzer.extract_insights(jobs)
            assert insights["total_jobs"] == 1
            print("✅ BaseResultsAnalyzer insights extraction working")
        except Exception as e:
            print(f"❌ Insights test failed: {e}")
            raise
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions."""
    print("\nTesting utility functions...")
    
    try:
        from job_collector.core.utils import (
            generate_job_id, clean_text, extract_salary_range,
            extract_skills_from_text, normalize_location
        )
        
        # Test ID generation
        job_id = generate_job_id("Software Engineer", "Test Company", "test")
        assert len(job_id) == 16
        print("✅ Job ID generation working")
        
        # Test text cleaning
        cleaned = clean_text("  Test   text  with   extra   spaces  ")
        assert cleaned == "Test text with extra spaces"
        print("✅ Text cleaning working")
        
        # Test salary extraction
        min_sal, max_sal, currency = extract_salary_range("$80,000 - $120,000")
        assert min_sal == 80000 and max_sal == 120000
        print("✅ Salary extraction working")
        
        # Test skill extraction
        skills = extract_skills_from_text("Looking for Python and JavaScript developers")
        assert "python" in skills and "javascript" in skills
        print("✅ Skill extraction working")
        
        # Test location normalization
        location = normalize_location("remote")
        assert location == "Remote"
        print("✅ Location normalization working")
        
        return True
        
    except Exception as e:
        print(f"❌ Utility function test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("PHASE 1 COMPLETION VERIFICATION")
    print("=" * 60)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_imports()
    all_passed &= test_basic_functionality()
    all_passed &= test_utility_functions()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 PHASE 1 COMPLETE! All tests passed.")
        print("\nFoundation components implemented:")
        print("✅ Modular project structure")
        print("✅ Complete data models with serialization")
        print("✅ Comprehensive configuration system")
        print("✅ Abstract interfaces for all components")
        print("✅ Custom exception hierarchy")
        print("✅ Structured logging system")
        print("✅ Utility functions for common operations")
        print("✅ Main JobCollector orchestrator")
        print("✅ Base results analyzer implementation")
        print("\nReady for Phase 2: Data Source Integration")
    else:
        print("❌ PHASE 1 INCOMPLETE - Some tests failed")
        return 1
    
    print("=" * 60)
    return 0

if __name__ == "__main__":
    sys.exit(main())
