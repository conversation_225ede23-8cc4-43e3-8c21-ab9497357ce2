#!/usr/bin/env python3
"""
Minimal CUA test to verify basic functionality.
"""

import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


async def test_cua_basic():
    """Test basic CUA functionality."""
    print("🤖 Testing Basic CUA Functionality")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing CUA imports...")
        from computer import Computer
        from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
        print("✅ CUA modules imported successfully")
        
        # Test Computer initialization
        print("💻 Testing Computer initialization...")
        computer = Computer(os_type="macos")
        print("✅ Computer initialized")
        
        # Test LLM initialization
        print("🧠 Testing LLM initialization...")

        # Use OpenAI for now
        print("Using OpenAI GPT-4o...")
        llm = LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
        loop = AgentLoop.OPENAI

        print("✅ LLM initialized")

        # Test Agent initialization
        print("🤖 Testing Agent initialization...")
        agent = ComputerAgent(
            computer=computer,
            model=llm,
            loop=loop
        )
        print("✅ Agent initialized")
        
        # Test simple task
        print("🎯 Testing simple task...")
        task = "Take a screenshot of the current screen"
        
        print(f"Running task: {task}")
        async for result in agent.run(task):
            print(f"Result: {result}")
            break  # Just get first result
        
        print("✅ Basic CUA test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ CUA test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the minimal CUA test."""
    print("🚀 Minimal CUA Test")
    print("=" * 30)
    
    # Check environment
    if not os.getenv('OPENAI_API_KEY') and not os.getenv('ANTHROPIC_API_KEY'):
        print("❌ No API keys found. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY")
        return False
    
    success = await test_cua_basic()
    
    if success:
        print("\n🎉 CUA is working! Ready for job collection.")
    else:
        print("\n❌ CUA test failed. Check your setup.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
