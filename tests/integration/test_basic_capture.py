#!/usr/bin/env python3
"""
Minimal test to validate basic screen capture functionality.

This script tests the most fundamental component: can we capture the VM screen?
If this fails, the problem is in the VM/display configuration, not in OmniParser.
"""

import os
import asyncio
from datetime import datetime

try:
    from computer import Computer
    from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
    CUA_AVAILABLE = True
except ImportError:
    CUA_AVAILABLE = False
    print("❌ CUA framework not available. Install with: pip install cua-agent[omni]")
    exit(1)


async def test_basic_capture():
    """Test basic screen capture without any agent involvement."""
    print("🔍 Testing Basic Screen Capture")
    print("=" * 40)
    
    # Create debug directory
    debug_dir = "debug_basic"
    os.makedirs(debug_dir, exist_ok=True)
    
    try:
        async with Computer() as computer:
            print("✅ Computer object created successfully")
            
            # Test 1: Inspect Computer object
            print("\n🔍 Test 1: Inspect Computer object methods")
            print("Available methods:")
            methods = [method for method in dir(computer) if not method.startswith('_')]
            for method in methods:
                print(f"  - {method}")

            # Test 2: Try different capture methods
            print("\n📸 Test 2: Try different screen capture methods")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Try various possible method names
            capture_methods = ['capture_screen', 'screenshot', 'capture', 'take_screenshot', 'get_screenshot']

            for method_name in capture_methods:
                if hasattr(computer, method_name):
                    print(f"✅ Found method: {method_name}")
                    try:
                        method = getattr(computer, method_name)
                        if callable(method):
                            capture_path = f"{debug_dir}/basic_capture_{method_name}_{timestamp}.png"
                            result = await method()
                            if hasattr(result, 'save'):
                                await result.save(capture_path)
                            else:
                                # Maybe it returns bytes or PIL image
                                with open(capture_path, 'wb') as f:
                                    f.write(result)
                            print(f"✅ Screenshot saved using {method_name}: {capture_path}")
                            break
                    except Exception as e:
                        print(f"⚠️ Method {method_name} failed: {e}")
                else:
                    print(f"❌ Method {method_name} not found")

            print(f"✅ Computer object inspection completed")
            
            # Test 3: Try with ComputerAgent
            print("\n🤖 Test 3: Test with ComputerAgent")
            try:
                # Create agent to see if it has screenshot capabilities
                agent = ComputerAgent(
                    computer=computer,
                    loop=AgentLoop.OMNI,
                    model=LLM(provider=LLMProvider.OPENAI, name="gpt-4o")
                )
                print("✅ ComputerAgent created successfully")

                # Check agent methods
                print("ComputerAgent methods:")
                agent_methods = [method for method in dir(agent) if not method.startswith('_')]
                for method in agent_methods:
                    print(f"  - {method}")

                # Check if agent.computer has different methods
                if hasattr(agent, 'computer'):
                    print("\nAgent.computer methods:")
                    computer_methods = [method for method in dir(agent.computer) if not method.startswith('_')]
                    for method in computer_methods:
                        print(f"  - {method}")

            except Exception as e:
                print(f"⚠️ ComputerAgent test failed: {e}")

            # Test 4: Try to get screen size info
            print("\n📊 Test 4: Screen information")
            try:
                if hasattr(computer, 'get_screenshot_size'):
                    size = await computer.get_screenshot_size()
                    print(f"✅ Screenshot size: {size}")
                else:
                    print("❌ get_screenshot_size method not available")
            except Exception as e:
                print(f"⚠️ Could not get screen size: {e}")

            print(f"\n🏁 Basic capture tests completed!")
            print(f"📁 Check debug directory: {debug_dir}/")
            print("\nFindings:")
            print("- Computer object created successfully")
            print("- No direct screenshot methods found on Computer object")
            print("- Need to investigate ComputerAgent screenshot capabilities")
            
    except Exception as e:
        print(f"❌ Basic capture test failed: {e}")
        print("\nThis indicates a fundamental issue with:")
        print("- CUA installation")
        print("- LUME VM configuration") 
        print("- Computer object initialization")


if __name__ == "__main__":
    asyncio.run(test_basic_capture())
