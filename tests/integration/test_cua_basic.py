#!/usr/bin/env python3
"""
Basic CUA functionality test script.

This script tests the core CUA components and verifies that the framework
is properly configured for job collection tasks.
"""

import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


async def test_cua_configuration():
    """Test CUA configuration and basic functionality."""
    print("🔧 Testing CUA Configuration...")
    
    try:
        # Test imports
        from computer import Computer
        from agent import ComputerAgent, LLM, LLMProvider
        print("✓ CUA modules imported successfully")
        
        # Test LLM configuration
        provider = os.getenv('DEFAULT_PROVIDER', 'openai')
        model = os.getenv('DEFAULT_MODEL', 'gpt-4o')
        
        print(f"✓ Using provider: {provider}")
        print(f"✓ Using model: {model}")
        
        # Create LLM instance
        if provider == 'openai':
            llm = LLM(provider=LLMProvider.OPENAI, name=model)
        elif provider == 'anthropic':
            llm = LLM(provider=LLMProvider.ANTHROPIC, name=model)
        else:
            raise ValueError(f"Unsupported provider: {provider}")
            
        print("✓ LLM instance created successfully")
        
        # Test environment variables
        api_key_var = f"{provider.upper()}_API_KEY"
        api_key = os.getenv(api_key_var)
        
        if not api_key or api_key.startswith('your_'):
            print(f"⚠️  Warning: {api_key_var} not properly configured")
            return False
        else:
            print(f"✓ {api_key_var} is configured")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_computer_interface():
    """Test Computer interface (without starting VM)."""
    print("\n💻 Testing Computer Interface...")
    
    try:
        from computer import Computer
        
        # Test creating computer instance (cloud mode for testing)
        print("✓ Computer class available")
        print("✓ Ready for VM-based automation")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


async def test_agent_interface():
    """Test ComputerAgent interface."""
    print("\n🤖 Testing Agent Interface...")
    
    try:
        from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
        
        # Test available agent loops
        print("Available agent loops:")
        for loop in AgentLoop:
            print(f"  - {loop.value}")
        
        print("✓ Agent interface ready")
        print("✓ Multiple agent loops available")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


async def main():
    """Run all CUA tests."""
    print("🚀 CUA Framework Test Suite")
    print("=" * 40)
    
    tests = [
        ("Configuration", test_cua_configuration),
        ("Computer Interface", test_computer_interface),
        ("Agent Interface", test_agent_interface),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! CUA is ready for job collection.")
        return True
    else:
        print("⚠️  Some tests failed. Please check configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
