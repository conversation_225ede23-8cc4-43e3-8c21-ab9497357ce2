[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cua-job-collector"
version = "0.1.0"
description = "CUA-powered job collection automation system"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "CUA Job Collector Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.11"
dependencies = [
    # Core dependencies
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "click>=8.0.0",
    
    # Data processing
    "pandas>=2.0.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    
    # HTTP and web
    "requests>=2.31.0",
    "httpx>=0.24.0",
    
    # Browser automation (fallback)
    "selenium>=4.15.0",
    
    # CUA Framework
    "cua-computer[all]>=0.2.10",
    "cua-agent[all]>=0.2.10",
    
    # Logging and monitoring
    "structlog>=23.0.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.22.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "responses>=0.23.0",
]

[project.scripts]
cua-collect = "scripts.run_collection:main"

[project.urls]
Homepage = "https://github.com/nebitessema/cua_agent"
Repository = "https://github.com/nebitessema/cua_agent"
Documentation = "https://github.com/nebitessema/cua_agent/docs"
"Bug Tracker" = "https://github.com/nebitessema/cua_agent/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
job_collector = ["py.typed"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["job_collector"]

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "cua: marks tests that require CUA/LUME VM",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# MyPy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "selenium.*",
    "beautifulsoup4.*",
    "lxml.*",
    "computer.*",
    "agent.*",
]
ignore_missing_imports = true
