# Environment files
.env
.env.local
.env.production

# Virtual environment
venv/
env/
ENV/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Output directories (but keep structure)
output/*
!output/.gitkeep
!output/screenshots/
!output/logs/
!output/results/
output/screenshots/*
!output/screenshots/.gitkeep
output/logs/*
!output/logs/.gitkeep
output/results/*
!output/results/.gitkeep

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup
*.tmp

# Test files
test_output/
temp/
demo_output/

# Project specific
*.tar.gz
*.zip
agent_transfer.*
cua_agent_transfer.*

# API keys and secrets
secrets.yaml
api_keys.txt

# Temporary files and outputs
temp/
trajectories/
*.pyc
__pycache__/
.pytest_cache/

# IDE files
.idea/
.vscode/
*.iml

# Virtual environment
venv/
env/

# CUA specific
lume_data/
vm_snapshots/

# All test outputs (centralized)
output/
session_recordings/
debug*/
*.mp4
*.mov
*.avi
*.mkv
*.webm
*.flv
*.wmv
