# ADR-001: Agent Deployment Approaches

**Status:** Accepted  
**Date:** 2025-06-11  
**Authors: <AUTHORS>
**Reviewers:** -  

## Context

The CUA (Computer Use Agent) project required a practical deployment strategy that would allow for both development flexibility and production-ready execution. During the development process, we explored multiple approaches to deploy and run the agent, each with different trade-offs in terms of complexity, isolation, performance, and ease of use.

## Problem Statement

We needed to determine the optimal deployment approach for a computer-use agent that could:

1. **Execute reliably** across different environments
2. **Integrate with AI providers** (Anthrop<PERSON> Claude, OpenAI)
3. **Provide isolation** for security and testing
4. **Support development workflows** with quick iteration
5. **Scale to production use** when needed
6. **Leverage the CUA framework** effectively

## Decision Drivers

- **Ease of setup and use** for developers
- **Security and isolation** for agent execution
- **Integration complexity** with existing CUA framework
- **Performance and resource usage**
- **Debugging and troubleshooting capabilities**
- **Scalability and production readiness**

## Options Considered

### Option 1: Direct CUA Framework Integration

**Approach:** Deep integration with the full CUA framework installed at `~/util/cua/`

**Implementation Attempted:**
```python
# main.py - Complex CUA framework integration
from cua.agent import Agent
from cua.computer import Computer
from cua.core import CUACore

agent = Agent(
    provider="anthropic",
    model="claude-3-5-sonnet-20241022",
    computer=Computer()
)
```

**Pros:**
- Full access to CUA framework capabilities
- Native computer interaction features
- Advanced automation possibilities
- Consistent with CUA ecosystem

**Cons:**
- Complex dependency management
- Frequent import errors and version conflicts
- Difficult to debug framework issues
- Heavy resource requirements
- Steep learning curve for new developers

**Outcome:** Abandoned due to complexity and reliability issues

### Option 2: Simplified Local Agent

**Approach:** Lightweight Python implementation with minimal dependencies

**Implementation:**
```python
# simple_agent.py - Standalone implementation
class SimpleAgent:
    def __init__(self):
        self.anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    
    def run_task(self, task_name, **kwargs):
        # Direct task implementation
```

**Pros:**
- Easy to understand and modify
- Minimal dependencies
- Fast startup and execution
- Excellent for development and testing
- Clear debugging and error handling

**Cons:**
- Limited to basic automation tasks
- No advanced computer interaction
- Manual implementation of common patterns
- Less integration with CUA ecosystem

**Outcome:** Successful for development and basic automation

### Option 3: VM-Based Execution with LUME

**Approach:** Run agents in isolated macOS virtual machines using LUME

**Implementation:**
```bash
# LUME VM deployment
lume run macos-sequoia-cua_latest --shared-dir ~/workspace/spring25/cua_agent:rw
ssh lume@************
python simple_agent.py --interactive
```

**Pros:**
- Complete isolation and security
- Consistent execution environment
- Easy to reset and reproduce issues
- Supports both development and production
- Leverages Apple's Virtualization.Framework
- Near-native performance on Apple Silicon

**Cons:**
- Additional setup complexity
- Resource overhead (CPU, memory, disk)
- Network configuration requirements
- VM management overhead

**Outcome:** Recommended for production and secure execution

## Decision

**We decided to implement a hybrid approach with multiple deployment options:**

1. **Primary: Simplified Local Agent** (`simple_agent.py`)
   - For development, testing, and basic automation
   - Minimal dependencies and maximum reliability
   - Rich terminal interface for interactive use

2. **Secondary: VM-Based Deployment** (LUME + simple_agent.py)
   - For production, security-sensitive tasks, and isolation
   - Shared directory approach for easy code transfer
   - Full macOS environment with CUA framework available

3. **Legacy: CUA Framework Integration** (`main.py`)
   - Maintained for reference and future enhancement
   - Demonstrates advanced CUA framework usage
   - Available for complex automation scenarios

## Rationale

### Why Simplified Local Agent as Primary?

1. **Developer Experience**: Immediate productivity without complex setup
2. **Reliability**: Fewer dependencies mean fewer failure points
3. **Debugging**: Clear error messages and straightforward troubleshooting
4. **Iteration Speed**: Fast development and testing cycles
5. **Learning Curve**: Easy for new contributors to understand

### Why VM-Based as Secondary?

1. **Security**: Complete isolation from host system
2. **Reproducibility**: Consistent environment across deployments
3. **Scalability**: Can run multiple isolated agents
4. **Production Ready**: Suitable for automated deployment scenarios
5. **CUA Integration**: Full framework available when needed

### Why Keep CUA Framework Integration?

1. **Future Proofing**: Advanced features may require full framework
2. **Reference Implementation**: Shows proper CUA usage patterns
3. **Migration Path**: Easy upgrade when framework stabilizes
4. **Community Alignment**: Maintains connection to CUA ecosystem

## Implementation Details

### LUME Technical Capabilities

LUME provides sophisticated virtualization features:

- **Apple Virtualization.Framework**: Native macOS VM support
- **VNC Access**: Visual desktop interaction (`vnc://127.0.0.1:port`)
- **SSH Access**: Command-line interface for automation
- **Shared Directories**: Seamless file sharing between host and VM
- **Container Registry**: Pre-built macOS images with CUA support
- **API Server**: HTTP API for programmatic VM management
- **Resource Management**: Configurable CPU, memory, and disk allocation

### Deployment Architecture

```
Host macOS System
├── ~/workspace/spring25/cua_agent/     # Development environment
│   ├── simple_agent.py                # Primary agent implementation
│   ├── main.py                        # CUA framework integration
│   └── requirements.txt               # Dependencies
│
├── ~/util/cua/                        # CUA framework installation
│   ├── libs/lume/                     # LUME virtualization system
│   └── libs/agent/                    # CUA agent framework
│
└── LUME VM (macos-sequoia-cua_latest)  # Isolated execution environment
    ├── /Volumes/My Shared Files/       # Shared agent code
    ├── SSH: lume@************         # Command-line access
    └── VNC: vnc://127.0.0.1:port      # Visual access
```

## Consequences

### Positive

- **Multiple deployment options** support different use cases
- **Low barrier to entry** for new developers
- **Production-ready isolation** when needed
- **Clear upgrade path** to advanced features
- **Excellent debugging experience** in development
- **Security isolation** for sensitive operations

### Negative

- **Multiple codebases** to maintain (simple_agent.py vs main.py)
- **Documentation complexity** covering multiple approaches
- **Resource overhead** for VM-based deployment
- **Learning curve** for LUME VM management
- **Potential fragmentation** between approaches

### Neutral

- **Framework dependency** remains optional rather than required
- **VM management** adds operational complexity but improves security
- **Code duplication** between approaches but enables specialization

## Monitoring and Review

This decision should be reviewed when:

1. **CUA framework stability** improves significantly
2. **New deployment requirements** emerge (cloud, containers, etc.)
3. **Performance issues** arise with current approaches
4. **Security requirements** change
5. **Community feedback** suggests better alternatives

## Related Decisions

- Future ADR: Container-based deployment (Docker/Podman)
- Future ADR: Cloud deployment strategies
- Future ADR: Multi-agent orchestration approaches

## References

- [CUA Framework Repository](https://github.com/trycua/cua)
- [LUME Documentation](https://github.com/trycua/cua/tree/main/libs/lume)
- [Apple Virtualization Framework](https://developer.apple.com/documentation/virtualization)
- [Anthropic Claude API](https://docs.anthropic.com/)
