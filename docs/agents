<div align="center">
<h1>
  <div class="image-wrapper" style="display: inline-block;">
    <picture>
      <source media="(prefers-color-scheme: dark)" alt="logo" height="150" srcset="../../img/logo_white.png" style="display: block; margin: auto;">
      <source media="(prefers-color-scheme: light)" alt="logo" height="150" srcset="../../img/logo_black.png" style="display: block; margin: auto;">
      <img alt="Shows my svg">
    </picture>
  </div>

  [![Python](https://img.shields.io/badge/Python-333333?logo=python&logoColor=white&labelColor=333333)](#)
  [![macOS](https://img.shields.io/badge/macOS-000000?logo=apple&logoColor=F0F0F0)](#)
  [![Discord](https://img.shields.io/badge/Discord-%235865F2.svg?&logo=discord&logoColor=white)](https://discord.com/invite/mVnXXpdE85)
  [![PyPI](https://img.shields.io/pypi/v/cua-computer?color=333333)](https://pypi.org/project/cua-computer/)
</h1>
</div>

**cua-agent** is a general Computer-Use framework for running multi-app agentic workflows targeting macOS and Linux sandbox created with Cua, supporting local (Ollama) and cloud model providers (OpenAI, Anthropic, Groq, DeepSeek, Qwen).

### Get started with Agent

<div align="center">
    <img src="../../img/agent.png"/>
</div>

## Install

```bash
pip install "cua-agent[all]"

# or install specific loop providers
pip install "cua-agent[openai]" # OpenAI Cua Loop
pip install "cua-agent[anthropic]" # Anthropic Cua Loop
pip install "cua-agent[uitars]"    # UI-Tars support
pip install "cua-agent[omni]" # Cua Loop based on OmniParser (includes Ollama for local models)
pip install "cua-agent[ui]" # Gradio UI for the agent

# For local UI-TARS with MLX support, you need to manually install mlx-vlm:
pip install "cua-agent[uitars-mlx]"
pip install git+https://github.com/ddupont808/mlx-vlm.git@stable/fix/qwen2-position-id # PR: https://github.com/Blaizzy/mlx-vlm/pull/349
```

## Run

```bash
async with Computer() as macos_computer:
  # Create agent with loop and provider
  agent = ComputerAgent(
      computer=macos_computer,
      loop=AgentLoop.OPENAI,
      model=LLM(provider=LLMProvider.OPENAI)
      # or
      # loop=AgentLoop.ANTHROPIC,
      # model=LLM(provider=LLMProvider.ANTHROPIC)
      # or
      # loop=AgentLoop.OMNI,
      # model=LLM(provider=LLMProvider.OLLAMA, name="gemma3")
      # or
      # loop=AgentLoop.UITARS,
      # model=LLM(provider=LLMProvider.OAICOMPAT, name="ByteDance-Seed/UI-TARS-1.5-7B", provider_base_url="https://**************.us-east-1.aws.endpoints.huggingface.cloud/v1")
  )

  tasks = [
      "Look for a repository named trycua/cua on GitHub.",
      "Check the open issues, open the most recent one and read it.",
      "Clone the repository in users/lume/projects if it doesn't exist yet.",
      "Open the repository with an app named Cursor (on the dock, black background and white cube icon).",
      "From Cursor, open Composer if not already open.",
      "Focus on the Composer text area, then write and submit a task to help resolve the GitHub issue.",
  ]

  for i, task in enumerate(tasks):
      print(f"\nExecuting task {i}/{len(tasks)}: {task}")
      async for result in agent.run(task):
          print(result)

      print(f"\n✅ Task {i+1}/{len(tasks)} completed: {task}")
```

Refer to these notebooks for step-by-step guides on how to use the Computer-Use Agent (CUA):

- [Agent Notebook](../../notebooks/agent_nb.ipynb) - Complete examples and workflows

## Using the Gradio UI

The agent includes a Gradio-based user interface for easier interaction.

<div align="center">
    <img src="../../img/agent_gradio_ui.png"/>
</div>

To use it:

```bash
# Install with Gradio support
pip install "cua-agent[ui]"
```

### Create a simple launcher script

```python
# launch_ui.py
from agent.ui.gradio.app import create_gradio_ui

app = create_gradio_ui()
app.launch(share=False)
```

### Setting up API Keys

For the Gradio UI to show available models, you need to set API keys as environment variables:

```bash
# For OpenAI models
export OPENAI_API_KEY=your_openai_key_here

# For Anthropic models
export ANTHROPIC_API_KEY=your_anthropic_key_here

# Launch with both keys set
OPENAI_API_KEY=your_key ANTHROPIC_API_KEY=your_key python launch_ui.py
```

Without these environment variables, the UI will show "No models available" for the corresponding providers, but you can still use local models with the OMNI loop provider.

### Using Local Models

You can use local models with the OMNI loop provider by selecting "Custom model..." from the dropdown. The default provider URL is set to `http://localhost:1234/v1` which works with LM Studio.

If you're using a different local model server:
- vLLM: `http://localhost:8000/v1`
- LocalAI: `http://localhost:8080/v1`
- Ollama with OpenAI compat API: `http://localhost:11434/v1`

The Gradio UI provides:
- Selection of different agent loops (OpenAI, Anthropic, OMNI)
- Model selection for each provider
- Configuration of agent parameters
- Chat interface for interacting with the agent

### Using UI-TARS

The UI-TARS models are available in two forms:

1. **MLX UI-TARS models** (Default): These models run locally using MLXVLM provider
   - `mlx-community/UI-TARS-1.5-7B-4bit` (default) - 4-bit quantized version
   - `mlx-community/UI-TARS-1.5-7B-6bit` - 6-bit quantized version for higher quality

   ```python
   agent = ComputerAgent(
       computer=macos_computer,
       loop=AgentLoop.UITARS,
       model=LLM(provider=LLMProvider.MLXVLM, name="mlx-community/UI-TARS-1.5-7B-4bit")
   )
   ```

2. **OpenAI-compatible UI-TARS**: For using the original ByteDance model
   - If you want to use the original ByteDance UI-TARS model via an OpenAI-compatible API, follow the [deployment guide](https://github.com/bytedance/UI-TARS/blob/main/README_deploy.md)
   - This will give you a provider URL like `https://**************.us-east-1.aws.endpoints.huggingface.cloud/v1` which you can use in the code or Gradio UI:

   ```python
   agent = ComputerAgent(
       computer=macos_computer,
       loop=AgentLoop.UITARS,
       model=LLM(provider=LLMProvider.OAICOMPAT, name="tgi",
                provider_base_url="https://**************.us-east-1.aws.endpoints.huggingface.cloud/v1")
   )
   ```

## Agent Loops

The `cua-agent` package provides three agent loops variations, based on different CUA models providers and techniques:

| Agent Loop | Supported Models | Description | Set-Of-Marks |
|:-----------|:-----------------|:------------|:-------------|
| `AgentLoop.OPENAI` | • `computer_use_preview` | Use OpenAI Operator CUA model | Not Required |
| `AgentLoop.ANTHROPIC` | • `claude-3-5-sonnet-20240620`<br>• `claude-3-7-sonnet-20250219` | Use Anthropic Computer-Use | Not Required |
| `AgentLoop.UITARS` | • `mlx-community/UI-TARS-1.5-7B-4bit` (default)<br>• `mlx-community/UI-TARS-1.5-7B-6bit`<br>• `ByteDance-Seed/UI-TARS-1.5-7B` (via openAI-compatible endpoint) | Uses UI-TARS models with MLXVLM (default) or OAICOMPAT providers | Not Required |
| `AgentLoop.OMNI` | • `claude-3-5-sonnet-20240620`<br>• `claude-3-7-sonnet-20250219`<br>• `gpt-4.5-preview`<br>• `gpt-4o`<br>• `gpt-4`<br>• `phi4`<br>• `phi4-mini`<br>• `gemma3`<br>• `...`<br>• `Any Ollama or OpenAI-compatible model` | Use OmniParser for element pixel-detection (SoM) and any VLMs for UI Grounding and Reasoning | OmniParser |

## AgentResponse
The `AgentResponse` class represents the structured output returned after each agent turn. It contains the agent's response, reasoning, tool usage, and other metadata. The response format aligns with the new [OpenAI Agent SDK specification](https://platform.openai.com/docs/api-reference/responses) for better consistency across different agent loops.

```python
async for result in agent.run(task):
  print("Response ID: ", result.get("id"))

  # Print detailed usage information
  usage = result.get("usage")
  if usage:
      print("\nUsage Details:")
      print(f"  Input Tokens: {usage.get('input_tokens')}")
      if "input_tokens_details" in usage:
          print(f"  Input Tokens Details: {usage.get('input_tokens_details')}")
      print(f"  Output Tokens: {usage.get('output_tokens')}")
      if "output_tokens_details" in usage:
          print(f"  Output Tokens Details: {usage.get('output_tokens_details')}")
      print(f"  Total Tokens: {usage.get('total_tokens')}")

  print("Response Text: ", result.get("text"))

  # Print tools information
  tools = result.get("tools")
  if tools:
      print("\nTools:")
      print(tools)

  # Print reasoning and tool call outputs
  outputs = result.get("output", [])
  for output in outputs:
      output_type = output.get("type")
      if output_type == "reasoning":
          print("\nReasoning Output:")
          print(output)
      elif output_type == "computer_call":
          print("\nTool Call Output:")
          print(output)
```

**Note on Settings Persistence:**

*   The Gradio UI automatically saves your configuration (Agent Loop, Model Choice, Custom Base URL, Save Trajectory state, Recent Images count) to a file named `.gradio_settings.json` in the project's root directory when you successfully run a task.
*   This allows your preferences to persist between sessions.
*   API keys entered into the custom provider field are **not** saved in this file for security reasons. Manage API keys using environment variables (e.g., `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`) or a `.env` file.
*   It's recommended to add `.gradio_settings.json` to your `.gitignore` file.


---

using with claude?
cua-mcp-server is a MCP server for the Computer-Use Agent (CUA), allowing you to run CUA through Claude Desktop or other MCP clients.

Get started with Agent
Prerequisites
Before installing the MCP server, you'll need to set up the full Computer-Use Agent capabilities as described in Option 2 of the main README. This includes:

Installing the Lume CLI
Pulling the latest macOS CUA image
Starting the Lume daemon service
Installing the required Python libraries (Optional: only needed if you want to verify the agent is working before installing MCP server)
Make sure these steps are completed and working before proceeding with the MCP server installation.

Installation
Install the package from PyPI:

pip install cua-mcp-server
This will install:

The MCP server
CUA agent and computer dependencies
An executable cua-mcp-server script in your PATH
Easy Setup Script
If you want to simplify installation, you can use this one-liner to download and run the installation script:

curl -fsSL https://raw.githubusercontent.com/trycua/cua/main/libs/mcp-server/scripts/install_mcp_server.sh | bash
This script will:

Create the ~/.cua directory if it doesn't exist
Generate a startup script at ~/.cua/start_mcp_server.sh
Make the script executable
The startup script automatically manages Python virtual environments and installs/updates the cua-mcp-server package
You can then use the script in your MCP configuration like this:

{
  "mcpServers": {
    "cua-agent": {
      "command": "/bin/bash",
      "args": ["~/.cua/start_mcp_server.sh"],
      "env": {
        "CUA_AGENT_LOOP": "OMNI",
        "CUA_MODEL_PROVIDER": "ANTHROPIC",
        "CUA_MODEL_NAME": "claude-3-7-sonnet-20250219",
        "CUA_PROVIDER_API_KEY": "your-api-key"
      }
    }
  }
}
Development Guide
If you want to develop with the cua-mcp-server directly without installation, you can use this configuration:

{
  "mcpServers": {
    "cua-agent": {
      "command": "/bin/bash",
      "args": ["~/cua/libs/mcp-server/scripts/start_mcp_server.sh"],
      "env": {
        "CUA_AGENT_LOOP": "UITARS",
        "CUA_MODEL_PROVIDER": "OAICOMPAT",
        "CUA_MODEL_NAME": "ByteDance-Seed/UI-TARS-1.5-7B",
        "CUA_PROVIDER_BASE_URL": "https://****************.us-east-1.aws.endpoints.huggingface.cloud/v1",
        "CUA_PROVIDER_API_KEY": "your-api-key"
      }
    }
  }
}
This configuration:

Uses the start_mcp_server.sh script which automatically sets up the Python path and runs the server module
Works with Claude Desktop, Cursor, or any other MCP client
Automatically uses your development code without requiring installation
Just add this to your MCP client's configuration and it will use your local development version of the server.

Troubleshooting
If you get a /bin/bash: ~/cua/libs/mcp-server/scripts/start_mcp_server.sh: No such file or directory error, try changing the path to the script to be absolute instead of relative.

To see the logs:

tail -n 20 -f ~/Library/Logs/Claude/mcp*.log
Claude Desktop Integration
To use with Claude Desktop, add an entry to your Claude Desktop configuration (claude_desktop_config.json, typically found in ~/.config/claude-desktop/):

For more information on MCP with Claude Desktop, see the official MCP User Guide.

Cursor Integration
To use with Cursor, add an MCP configuration file in one of these locations:

Project-specific: Create .cursor/mcp.json in your project directory
Global: Create ~/.cursor/mcp.json in your home directory
After configuration, you can simply tell Cursor's Agent to perform computer tasks by explicitly mentioning the CUA agent, such as "Use the computer control tools to open Safari."

For more information on MCP with Cursor, see the official Cursor MCP documentation.

First-time Usage Notes
API Keys: Ensure you have valid API keys:

Add your Anthropic API key, or other model provider API key in the Claude Desktop config (as shown above)
Or set it as an environment variable in your shell profile
Configuration
The server is configured using environment variables (can be set in the Claude Desktop config):

Variable	Description	Default
CUA_AGENT_LOOP	Agent loop to use (OPENAI, ANTHROPIC, UITARS, OMNI)	OMNI
CUA_MODEL_PROVIDER	Model provider (ANTHROPIC, OPENAI, OLLAMA, OAICOMPAT)	ANTHROPIC
CUA_MODEL_NAME	Model name to use	None (provider default)
CUA_PROVIDER_BASE_URL	Base URL for provider API	None
CUA_MAX_IMAGES	Maximum number of images to keep in context	3
Available Tools
The MCP server exposes the following tools to Claude:

run_cua_task - Run a single Computer-Use Agent task with the given instruction
run_multi_cua_tasks - Run multiple tasks in sequence
Usage
Once configured, you can simply ask Claude to perform computer tasks:

"Open Chrome and go to github.com"
"Create a folder called 'Projects' on my desktop"
"Find all PDFs in my Downloads folder"
"Take a screenshot and highlight the error message"
Claude will automatically use your CUA agent to perform these tasks.

----
Using with mac


Cua AI Logo
Platform
Lume
Computer
Agent
MCP
Lumier
Pricing
Blog
Discord
github logo
8.6k
Sign In
Back to Articles
Build Your Own Operator on macOS - Part 1
Published on March 31, 2025 by The Cua Team

In this first blogpost, we'll learn how to build our own Computer-Use Operator using OpenAI's computer-use-preview model. But first, let's understand what some common terms mean:

A Virtual Machine (VM) is like a computer within your computer - a safe, isolated environment where the AI can work without affecting your main system.
computer-use-preview is OpenAI's specialized language model trained to understand and interact with computer interfaces through screenshots.
A Computer-Use Agent is an AI agent that can control a computer just like a human would - clicking buttons, typing text, and interacting with applications.
Our Operator will run in an isolated macOS VM, by making use of our cua-computer package and lume virtualization CLI.

Check out what it looks like to use your own Operator from a Gradio app:

What You'll Learn
By the end of this tutorial, you'll be able to:

Set up a macOS virtual machine for AI automation
Connect OpenAI's computer-use model to your VM
Create a basic loop for the AI to interact with your VM
Handle different types of computer actions (clicking, typing, etc.)
Implement safety checks and error handling
Prerequisites:

macOS Sonoma (14.0) or later
8GB RAM minimum (16GB recommended)
OpenAI API access (Tier 3+)
Basic Python knowledge
Familiarity with terminal commands
Estimated Time: 45-60 minutes

Introduction to Computer-Use Agents
Last March OpenAI released a fine-tuned version of GPT-4o, namely CUA, introducing pixel-level vision capabilities with advanced reasoning through reinforcement learning. This fine-tuning enables the computer-use model to interpret screenshots and interact with graphical user interfaces on a pixel-level such as buttons, menus, and text fields - mimicking human interactions on a computer screen. It scores a remarkable 38.1% success rate on OSWorld - a benchmark for Computer-Use agents on Linux and Windows. This is the 2nd available model after Anthropic's Claude 3.5 Sonnet to support computer-use capabilities natively with no external models (e.g. accessory SoM (Set-of-Mark) and OCR runs).

Professor Ethan Mollick provides an excellent explanation of computer-use agents in this article: When you give a Claude a mouse.

ChatGPT Operator
OpenAI's computer-use model powers ChatGPT Operator, a Chromium-based interface exclusively available to ChatGPT Pro subscribers. Users leverage this functionality to automate web-based tasks such as online shopping, expense report submission, and booking reservations by interacting with websites in a human-like manner.

Benefits of Custom Operators
Why Build Your Own?
While OpenAI's Operator uses a controlled Chromium VM instance, there are scenarios where you may want to use your own VM with full desktop capabilities. Here are some examples:

Automating native macOS apps like Finder, Xcode
Managing files, changing settings, and running terminal commands
Testing desktop software and applications
Creating workflows that combine web and desktop tasks
Automating media editing in apps like Final Cut Pro and Blender
This gives you more control and flexibility to automate tasks beyond just web browsing, with full access to interact with native applications and system-level operations. Additionally, running your own VM locally provides better privacy for sensitive user files and delivers superior performance by leveraging your own hardware instead of renting expensive Cloud VMs.

Access Requirements
Model Availability
As we speak, the computer-use-preview model has limited availability:

Only accessible to OpenAI tier 3+ users
Additional application process may be required even for eligible users
Cannot be used in the OpenAI Playground
Outside of ChatGPT Operator, usage is restricted to the new Responses API
Understanding the OpenAI API
Responses API Overview
Let's start with the basics. In our case, we'll use OpenAI's Responses API to communicate with their computer-use model.

Think of it like this:

We send the model a screenshot of our VM and tell it what we want it to do
The model looks at the screenshot and decides what actions to take
It sends back instructions (like "click here" or "type this")
We execute those instructions in our VM
The Responses API is OpenAI's newest way to interact with their AI models. It comes with several built-in tools:

Web search: Let the AI search the internet
File search: Help the AI find documents
Computer use: Allow the AI to control a computer (what we'll be using)
As we speak, the computer-use model is only available through the Responses API.

Responses API Examples
Let's look at some simple examples. We'll start with the traditional way of using OpenAI's API with Chat Completions, then show the new Responses API primitive.

Chat Completions:

python

# The old way required managing conversation history manually
messages = [{"role": "user", "content": "Hello"}]
response = client.chat.completions.create(
    model="gpt-4",
    messages=messages  # We had to track all messages ourselves
)
messages.append(response.choices[0].message)  # Manual message tracking
Responses API:

python

# Example 1: Simple web search
# The API handles all the complexity for us
response = client.responses.create(
    model="gpt-4",
    input=[{
        "role": "user",
        "content": "What's the latest news about AI?"
    }],
    tools=[{
        "type": "web_search",  # Tell the API to use web search
        "search_query": "latest AI news"
    }]
)

# Example 2: File search
# Looking for specific documents becomes easy
response = client.responses.create(
    model="gpt-4",
    input=[{
        "role": "user",
        "content": "Find documents about project X"
    }],
    tools=[{
        "type": "file_search",
        "query": "project X",
        "file_types": ["pdf", "docx"]  # Specify which file types to look for
    }]
)
Computer-Use Model Setup
For our operator, we'll use the computer-use model. Here's how we set it up:

python

# Set up the computer-use model to control our VM
response = client.responses.create(
    model="computer-use-preview",  # Special model for computer control
    tools=[{
        "type": "computer_use_preview",
        "display_width": 1024,     # Size of our VM screen
        "display_height": 768,
        "environment": "mac"       # Tell it we're using macOS.
    }],
    input=[
        {
            "role": "user",
            "content": [
                # What we want the AI to do
                {"type": "input_text", "text": "Open Safari and go to google.com"},
                # Current screenshot of our VM
                {"type": "input_image", "image_url": f"data:image/png;base64,{screenshot_base64}"}
            ]
        }
    ],
    truncation="auto"  # Let OpenAI handle message length
)
Understanding the Response
When we send a request, the API sends back a response that looks like this:

json

"output": [
    {
        "type": "reasoning",           # The AI explains what it's thinking
        "id": "rs_67cc...",
        "summary": [
            {
                "type": "summary_text",
                "text": "Clicking on the browser address bar."
            }
        ]
    },
    {
        "type": "computer_call",       # The actual action to perform
        "id": "cu_67cc...",
        "call_id": "call_zw3...",
        "action": {
            "type": "click",           # What kind of action (click, type, etc.)
            "button": "left",          # Which mouse button to use
            "x": 156,                  # Where to click (coordinates)
            "y": 50
        },
        "pending_safety_checks": [],   # Any safety warnings to consider
        "status": "completed"          # Whether the action was successful
    }
]
Each response contains:

Reasoning: The AI's explanation of what it's doing
Action: The specific computer action to perform
Safety Checks: Any potential risks to review
Status: Whether everything worked as planned
CUA-Computer Interface
Architecture Overview
Let's break down the main components of our system and how they work together:

The Virtual Machine (VM)

Think of this as a safe playground for our AI
It's a complete macOS system running inside your computer
Anything the AI does stays inside this VM, keeping your main system safe
We use lume to create and manage this VM
The Computer Interface (CUI)

This is how we control the VM
It can move the mouse, type text, and take screenshots
Works like a remote control for the VM
Built using our cua-computer package
The OpenAI Model

This is the brain of our operator
It looks at screenshots of the VM
Decides what actions to take
Sends back instructions like "click here" or "type this"
Here's how they all work together:

mermaid

OpenAI API
Virtual Machine
Computer Interface
You
OpenAI API
Virtual Machine
Computer Interface
You
The Main Loop
Each iteration
Execute the action
alt
[Mouse Click]
[Type Text]
[Scroll Screen]
[Press Keys]
[Wait]
loop
[Action Loop]
Start the operator
Create macOS sandbox
VM is ready
Take a screenshot
Return current screen
Send screenshot + instructions
Return next action
Move and click mouse
Type characters
Scroll window
Press keyboard keys
Pause for a moment
Task finished
All done!
The diagram above shows how information flows through our system:

You start the operator
The Computer Interface creates a virtual macOS
Then it enters a loop:
Take a picture of the VM screen
Send it to OpenAI with instructions
Get back an action to perform
Execute that action in the VM
Repeat until the task is done
This design keeps everything organized and safe. The AI can only interact with the VM through our controlled interface, and the VM keeps the AI's actions isolated from your main system.

Implementation Guide
Prerequisites
Lume CLI Setup For installing the standalone lume binary, run the following command from a terminal, or download the latest pkg.

bash

sudo /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/trycua/cua/main/libs/lume/scripts/install.sh)"
Once installed, start the lume daemon:

bash

lume serve
Next, pull the pre-built macOS VM image that contains all required dependencies:

bash

# Pull the latest macOS Sequoia image optimized for CUA
lume pull macos-sequoia-cua:latest --no-cache
Important Storage Notes:

Initial download requires 80GB of free space
After first run, space usage reduces to ~30GB due to macOS's sparse file system
VMs are stored in ~/.lume
Cached images are stored in ~/.lume/cache
Pro Tip: Remove --no-cache to save the image locally for reuse (requires 2x storage temporarily).

You can check your downloaded VM images anytime:

bash

lume ls
Example output:

name	os	cpu	memory	disk	display	status	ip	vnc
macos-sequoia-cua:latest	macOS	12	16.00G	64.5GB/80.0GB	1024x768	running	*************	vnc://:kind-forest-zulu-island@127.0.0.1:56085
After checking your available images, you can run the VM to ensure everything is working correctly:

bash

lume run macos-sequoia-cua:latest
Python Environment Setup Note: The cua-computer package requires Python 3.10 or later. We recommend creating a dedicated Python environment:

Using venv:

bash

python -m venv cua-env
source cua-env/bin/activate
Using conda:

bash

conda create -n cua-env python=3.10
conda activate cua-env
Then install the required packages:

bash

pip install openai
pip install cua-computer
Ensure you have an OpenAI API key (set as an environment variable or in your OpenAI configuration).

Building the Operator
Importing Required Modules
With the prerequisites installed and configured, we're ready to build our first operator. The following example uses asynchronous Python (async/await). You can run it either in a VS Code Notebook or as a standalone Python script.

python

import asyncio
import base64
import openai

from computer import Computer
Mapping API Actions to CUA Methods
The following helper function converts a computer_call action from the OpenAI Responses API into corresponding commands on the CUI interface. For example, if the API instructs a click action, we move the cursor and perform a left click on the lume VM Sandbox. We will use the computer interface to execute the actions.

python

async def execute_action(computer, action):
    action_type = action.type

    if action_type == "click":
        x = action.x
        y = action.y
        button = action.button
        print(f"Executing click at ({x}, {y}) with button '{button}'")
        await computer.interface.move_cursor(x, y)
        if button == "right":
            await computer.interface.right_click()
        else:
            await computer.interface.left_click()

    elif action_type == "type":
        text = action.text
        print(f"Typing text: {text}")
        await computer.interface.type_text(text)

    elif action_type == "scroll":
        x = action.x
        y = action.y
        scroll_x = action.scroll_x
        scroll_y = action.scroll_y
        print(f"Scrolling at ({x}, {y}) with offsets (scroll_x={scroll_x}, scroll_y={scroll_y})")
        await computer.interface.move_cursor(x, y)
        await computer.interface.scroll(scroll_y)  # Using vertical scroll only

    elif action_type == "keypress":
        keys = action.keys
        for key in keys:
            print(f"Pressing key: {key}")
            # Map common key names to CUA equivalents
            if key.lower() == "enter":
                await computer.interface.press_key("return")
            elif key.lower() == "space":
                await computer.interface.press_key("space")
            else:
                await computer.interface.press_key(key)

    elif action_type == "wait":
        wait_time = action.time
        print(f"Waiting for {wait_time} seconds")
        await asyncio.sleep(wait_time)

    elif action_type == "screenshot":
        print("Taking screenshot")
        # This is handled automatically in the main loop, but we can take an extra one if requested
        screenshot = await computer.interface.screenshot()
        return screenshot

    else:
        print(f"Unrecognized action: {action_type}")
Implementing the Computer-Use Loop
This section defines a loop that:

Initializes the cua-computer instance (connecting to a macOS sandbox).
Captures a screenshot of the current state.
Sends the screenshot (with a user prompt) to the OpenAI Responses API using the computer-use-preview model.
Processes the returned computer_call action and executes it using our helper function.
Captures an updated screenshot after the action (this example runs one iteration, but you can wrap it in a loop).
For a full loop, you would repeat these steps until no further actions are returned.

python

async def cua_openai_loop():
    # Initialize the lume computer instance (macOS sandbox)
    async with Computer(
        display="1024x768",
        memory="4GB",
        cpu="2",
        os="macos"
    ) as computer:
        await computer.run() # Start the lume VM

        # Capture the initial screenshot
        screenshot = await computer.interface.screenshot()
        screenshot_base64 = base64.b64encode(screenshot).decode('utf-8')

        # Initial request to start the loop
        response = openai.responses.create(
            model="computer-use-preview",
            tools=[{
                "type": "computer_use_preview",
                "display_width": 1024,
                "display_height": 768,
                "environment": "mac"
            }],
            input=[
                {
                    "role": "user",
                    "content": [
                        {"type": "input_text", "text": "Open Safari, download and install Cursor."},
                        {"type": "input_image", "image_url": f"data:image/png;base64,{screenshot_base64}"}
                    ]
                }
            ],
            truncation="auto"
        )

        # Continue the loop until no more computer_call actions
        while True:
            # Check for computer_call actions
            computer_calls = [item for item in response.output if item and item.type == "computer_call"]
            if not computer_calls:
                print("No more computer calls. Loop complete.")
                break

            # Get the first computer call
            call = computer_calls[0]
            last_call_id = call.call_id
            action = call.action
            print("Received action from OpenAI Responses API:", action)

            # Handle any pending safety checks
            if call.pending_safety_checks:
                print("Safety checks pending:", call.pending_safety_checks)
                # In a real implementation, you would want to get user confirmation here
                acknowledged_checks = call.pending_safety_checks
            else:
                acknowledged_checks = []

            # Execute the action
            await execute_action(computer, action)
            await asyncio.sleep(1)  # Allow time for changes to take effect

            # Capture new screenshot after action
            new_screenshot = await computer.interface.screenshot()
            new_screenshot_base64 = base64.b64encode(new_screenshot).decode('utf-8')

            # Send the screenshot back as computer_call_output
            response = openai.responses.create(
                model="computer-use-preview",
                tools=[{
                    "type": "computer_use_preview",
                    "display_width": 1024,
                    "display_height": 768,
                    "environment": "mac"
                }],
                input=[{
                    "type": "computer_call_output",
                    "call_id": last_call_id,
                    "acknowledged_safety_checks": acknowledged_checks,
                    "output": {
                        "type": "input_image",
                        "image_url": f"data:image/png;base64,{new_screenshot_base64}"
                    }
                }],
                truncation="auto"
            )

        # End the session
        await computer.stop()

# Run the loop
if __name__ == "__main__":
    asyncio.run(cua_openai_loop())
You can find the full code in our notebook.

Request Handling Differences
The first request to the OpenAI Responses API is special in that it includes the initial screenshot and prompt. Subsequent requests are handled differently, using the computer_call_output type to provide feedback on the executed action.

Initial Request Format
We use role: "user" with content that contains both input_text (the prompt) and input_image (the screenshot)
Subsequent Request Format
We use type: "computer_call_output" instead of the user role
We include the call_id to link the output to the specific previous action that was executed
We provide any acknowledged_safety_checks that were approved
We include the new screenshot in the output field
This structured approach allows the API to maintain context and continuity throughout the interaction session.

Note: For multi-turn conversations, you should include the previous_response_id in your initial requests when starting a new conversation with prior context. However, when using computer_call_output for action feedback, you don't need to explicitly manage the conversation history - OpenAI's API automatically tracks the context using the call_id. The previous_response_id is primarily important when the user provides additional instructions or when starting a new request that should continue from a previous session.

Conclusion
Summary
This blogpost demonstrates a single iteration of a OpenAI Computer-Use loop where:

A macOS sandbox is controlled using the CUA interface.
A screenshot and prompt are sent to the OpenAI Responses API.
The returned action (e.g. a click or type command) is executed via the CUI interface.
In a production setting, you would wrap the action-response cycle in a loop, handling multiple actions and safety checks as needed.

Next Steps
In the next blogpost, we'll introduce our Agent framework which abstracts away all these tedious implementation steps. This framework provides a higher-level API that handles the interaction loop between OpenAI's computer-use model and the macOS sandbox, allowing you to focus on building sophisticated applications rather than managing the low-level details we've explored here. Can't wait? Check out the cua-agent package!

Resources
OpenAI Computer-Use docs
cua-computer
lume
Share this post

←
Back to all posts

© 2025 Cua AI
GitHub
Discord
X (Twitter)
Privacy
Cookies


