"""CUA Session Manager - Proper VM lifecycle management.

This module provides a reliable way to manage CUA Computer instances with proper
async context handling, ensuring VMs actually start and shut down correctly.

Key Features:
- Proper async context manager for VM lifecycle
- Automatic agent creation with configurable providers
- Error handling for session state management
- Prevents "fake success" scenarios where VMs never actually start
"""

import asyncio
from typing import Optional
from computer import Computer
from agent import ComputerAgent, LLM, LLMProvider, AgentLoop


class CUASessionManager:
    """Manages CUA Computer lifecycle with proper async context handling.
    
    This class ensures that:
    1. VMs are actually started using proper async context management
    2. Agents are created with correct configuration
    3. Resources are properly cleaned up on exit
    4. Session state is validated before operations
    
    Usage:
        async with CUASessionManager() as session:
            computer = session.get_computer()
            agent = session.get_agent()
            # Use computer and agent...
        # VM automatically shuts down here
    """
    
    def __init__(self, os_type: str = "macos", provider: str = "openai", model: str = "gpt-4o"):
        """Initialize session manager with configuration.
        
        Args:
            os_type: Operating system type for the VM (default: "macos")
            provider: AI provider ("openai" or "anthropic")
            model: Model name to use (e.g., "gpt-4o", "claude-3-5-sonnet-20241022")
        """
        self.os_type = os_type
        self.provider = provider
        self.model = model
        self._computer: Optional[Computer] = None
        self._agent: Optional[ComputerAgent] = None
        self._session_active = False
    
    async def __aenter__(self):
        """Start the VM and initialize the session.
        
        This is the critical fix - we properly start the VM using the
        async context manager pattern instead of just creating a Computer object.
        """
        print("🚀 CUASessionManager: Starting VM...")
        
        try:
            # Create Computer instance
            self._computer = Computer(os_type=self.os_type)
            
            # This is the key fix - actually start the VM!
            await self._computer.__aenter__()
            
            self._session_active = True
            print("✅ CUASessionManager: VM active and ready")
            print(f"   OS Type: {self.os_type}")
            print(f"   AI Provider: {self.provider}")
            print(f"   Model: {self.model}")
            
            return self
            
        except Exception as e:
            print(f"❌ CUASessionManager: Failed to start VM: {e}")
            # Clean up on failure
            if self._computer:
                try:
                    await self._computer.__aexit__(type(e), e, e.__traceback__)
                except:
                    pass
                self._computer = None
            raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Shut down the VM and clean up resources."""
        print("🔄 CUASessionManager: Shutting down...")
        
        try:
            if self._computer and self._session_active:
                await self._computer.__aexit__(exc_type, exc_val, exc_tb)
                print("✅ CUASessionManager: VM shut down successfully")
        except Exception as e:
            print(f"⚠️ CUASessionManager: Error during shutdown: {e}")
        finally:
            # Always clean up state
            self._computer = None
            self._agent = None
            self._session_active = False
            print("✅ CUASessionManager: Cleanup complete")
    
    def get_computer(self) -> Computer:
        """Get the active Computer instance.
        
        Returns:
            Computer: The active Computer instance
            
        Raises:
            RuntimeError: If session is not active
        """
        if not self._session_active or not self._computer:
            raise RuntimeError(
                "Session not active. Use 'async with CUASessionManager()' to start a session."
            )
        return self._computer
    
    def get_agent(self) -> ComputerAgent:
        """Get or create a ComputerAgent instance.
        
        Creates the agent on first call and reuses it for subsequent calls.
        
        Returns:
            ComputerAgent: The configured agent instance
            
        Raises:
            RuntimeError: If session is not active
            ValueError: If provider is not supported
        """
        if not self._session_active or not self._computer:
            raise RuntimeError(
                "Session not active. Use 'async with CUASessionManager()' to start a session."
            )
        
        if not self._agent:
            print(f"🤖 Creating agent with {self.provider}:{self.model}")
            
            # Create LLM based on provider
            if self.provider == "openai":
                llm = LLM(provider=LLMProvider.OPENAI, name=self.model)
            elif self.provider == "anthropic":
                llm = LLM(provider=LLMProvider.ANTHROPIC, name=self.model)
            else:
                raise ValueError(f"Unsupported provider: {self.provider}. Use 'openai' or 'anthropic'.")
            
            # Create agent with OMNI loop for compatibility
            self._agent = ComputerAgent(
                computer=self._computer,
                loop=AgentLoop.OMNI,  # Use OMNI loop for best compatibility
                model=llm
            )
            
            print("✅ Agent created successfully")
        
        return self._agent
    
    @property
    def is_active(self) -> bool:
        """Check if the session is currently active."""
        return self._session_active and self._computer is not None
    
    async def take_screenshot(self) -> bytes:
        """Take a screenshot using the active computer.
        
        Convenience method for common operation.
        
        Returns:
            bytes: Screenshot data
        """
        computer = self.get_computer()
        return await computer.interface.screenshot()
    
    async def run_agent_task(self, task: str) -> str:
        """Run a single task with the agent and return the first response.
        
        Convenience method for simple agent interactions.
        
        Args:
            task: Task description for the agent
            
        Returns:
            str: First response from the agent
        """
        agent = self.get_agent()
        
        print(f"🤖 Running task: {task}")
        
        async for response in agent.run(task):
            print(f"   Agent response: {response}")
            return str(response)
        
        return "No response received"


# Convenience function for simple use cases
async def run_cua_task(task: str, provider: str = "openai", model: str = "gpt-4o") -> str:
    """Run a single CUA task with automatic session management.
    
    This is a convenience function for simple one-off tasks.
    For multiple tasks, use CUASessionManager directly to avoid VM startup overhead.
    
    Args:
        task: Task description for the agent
        provider: AI provider ("openai" or "anthropic")
        model: Model name to use
        
    Returns:
        str: Agent response
    """
    async with CUASessionManager(provider=provider, model=model) as session:
        return await session.run_agent_task(task)
