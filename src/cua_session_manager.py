"""CUA Session Manager - Proper VM lifecycle management.

This module provides a reliable way to manage CUA Computer instances with proper
async context handling, ensuring VMs actually start and shut down correctly.

Key Features:
- Proper async context manager for VM lifecycle
- Automatic agent creation with configurable providers
- Error handling for session state management
- Prevents "fake success" scenarios where VMs never actually start
"""

import asyncio
from typing import Optional
from computer import Computer, VMProviderType
from agent import ComputerAgent, LLM, LLMProvider, AgentLoop
import logging


class CUASessionManager:
    """Manages CUA Computer lifecycle with proper async context handling.
    
    This class ensures that:
    1. VMs are actually started using proper async context management
    2. Agents are created with correct configuration
    3. Resources are properly cleaned up on exit
    4. Session state is validated before operations
    
    Usage:
        async with CUASessionManager() as session:
            computer = session.get_computer()
            agent = session.get_agent()
            # Use computer and agent...
        # VM automatically shuts down here
    """
    
    def __init__(self, os_type: str = "macos", provider: str = "openai", model: str = "gpt-3.5-turbo"):
        """Initialize session manager with configuration.
        
        Args:
            os_type: Operating system type for the VM (default: "macos")
            provider: AI provider ("openai" or "anthropic")
            model: Model name to use (e.g., "gpt-4o", "claude-3-5-sonnet-20241022")
        """
        self.os_type = os_type
        self.provider = provider
        self.model = model
        self._computer: Optional[Computer] = None
        self._agent: Optional[ComputerAgent] = None
        self._session_active = False
    
    async def __aenter__(self):
        """Start the VM and initialize the session.

        Using the correct CUA pattern: Computer(os_type="macos") + await computer.run()
        as shown in the trycua/cua README, not the async context manager pattern.
        """
        print("🚀 CUASessionManager: Starting VM...")

        try:
            # Create Computer instance with proper configuration (matching trycua examples)
            self._computer = Computer(
                display="1024x768",
                memory="8GB",
                cpu="4",
                os_type=self.os_type,
                name=f"{self.os_type}-cua-session",
                verbosity=logging.INFO,
                provider_type=VMProviderType.LUME,
                ephemeral=False,
            )

            # Use the correct CUA pattern from the README
            await self._computer.run()

            self._session_active = True
            print("✅ CUASessionManager: VM active and ready")
            print(f"   OS Type: {self.os_type}")
            print(f"   AI Provider: {self.provider}")
            print(f"   Model: {self.model}")

            return self

        except Exception as e:
            print(f"❌ CUASessionManager: Failed to start VM: {e}")
            # Clean up on failure
            self._computer = None
            raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Shut down the VM and clean up resources."""
        print("🔄 CUASessionManager: Shutting down...")

        try:
            if self._computer and self._session_active:
                # Check if Computer has a stop() method, otherwise try close()
                if hasattr(self._computer, 'stop'):
                    await self._computer.stop()
                elif hasattr(self._computer, 'close'):
                    await self._computer.close()
                print("✅ CUASessionManager: VM shut down successfully")
        except Exception as e:
            print(f"⚠️ CUASessionManager: Error during shutdown: {e}")
        finally:
            # Always clean up state
            self._computer = None
            self._agent = None
            self._session_active = False
            print("✅ CUASessionManager: Cleanup complete")
    
    def get_computer(self) -> Computer:
        """Get the active Computer instance.
        
        Returns:
            Computer: The active Computer instance
            
        Raises:
            RuntimeError: If session is not active
        """
        if not self._session_active or not self._computer:
            raise RuntimeError(
                "Session not active. Use 'async with CUASessionManager()' to start a session."
            )
        return self._computer
    
    def get_agent(self) -> ComputerAgent:
        """Get or create a ComputerAgent instance.
        
        Creates the agent on first call and reuses it for subsequent calls.
        
        Returns:
            ComputerAgent: The configured agent instance
            
        Raises:
            RuntimeError: If session is not active
            ValueError: If provider is not supported
        """
        if not self._session_active or not self._computer:
            raise RuntimeError(
                "Session not active. Use 'async with CUASessionManager()' to start a session."
            )
        
        if not self._agent:
            print(f"🤖 Creating agent with {self.provider}:{self.model}")
            
            # Create LLM and Agent with proper configuration (matching trycua examples)
            if self.provider == "openai":
                llm = LLM(provider=LLMProvider.OPENAI, name=self.model)
                agent_loop = AgentLoop.OPENAI  # Use OPENAI loop for OpenAI provider
            elif self.provider == "anthropic":
                llm = LLM(provider=LLMProvider.ANTHROPIC, name=self.model)
                agent_loop = AgentLoop.ANTHROPIC  # Use ANTHROPIC loop for Anthropic provider
            else:
                raise ValueError(f"Unsupported provider: {self.provider}. Use 'openai' or 'anthropic'.")

            # Create agent with proper loop matching the provider
            self._agent = ComputerAgent(
                computer=self._computer,
                loop=agent_loop,  # Use provider-specific loop
                model=llm,
                save_trajectory=True,
                only_n_most_recent_images=3,
                verbosity=logging.INFO,
            )
            
            print("✅ Agent created successfully")
        
        return self._agent
    
    @property
    def is_active(self) -> bool:
        """Check if the session is currently active."""
        return self._session_active and self._computer is not None
    
    async def take_screenshot(self) -> bytes:
        """Take a screenshot using the active computer.
        
        Convenience method for common operation.
        
        Returns:
            bytes: Screenshot data
        """
        computer = self.get_computer()
        return await computer.interface.screenshot()
    
    async def run_agent_task(self, task: str) -> str:
        """Run a single task with the agent and return the first response.
        
        Convenience method for simple agent interactions.
        
        Args:
            task: Task description for the agent
            
        Returns:
            str: First response from the agent
        """
        agent = self.get_agent()
        
        print(f"🤖 Running task: {task}")
        
        async for response in agent.run(task):
            print(f"   Agent response: {response}")
            return str(response)
        
        return "No response received"


# Convenience function for simple use cases
async def run_cua_task(task: str, provider: str = "openai", model: str = "gpt-3.5-turbo") -> str:
    """Run a single CUA task with automatic session management.
    
    This is a convenience function for simple one-off tasks.
    For multiple tasks, use CUASessionManager directly to avoid VM startup overhead.
    
    Args:
        task: Task description for the agent
        provider: AI provider ("openai" or "anthropic")
        model: Model name to use
        
    Returns:
        str: Agent response
    """
    async with CUASessionManager(provider=provider, model=model) as session:
        return await session.run_agent_task(task)
